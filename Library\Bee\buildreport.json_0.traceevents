{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754351714784544, "dur": 8138, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754351714792689, "dur": 139528, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754351714932229, "dur": 3642, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 54336389, "ts": 1754351716494331, "dur": 1133, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714782258, "dur": 19724, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714801986, "dur": 1679809, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714803239, "dur": 2439, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714805684, "dur": 1826, "ph": "X", "name": "ProcessMessages 535", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714807514, "dur": 264, "ph": "X", "name": "ReadAsync 535", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714807782, "dur": 255, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714808040, "dur": 6820, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714814865, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714814869, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714814891, "dur": 583, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351714815477, "dur": 1650334, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716465818, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716465822, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716465858, "dur": 1733, "ph": "X", "name": "ProcessMessages 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716467594, "dur": 3718, "ph": "X", "name": "ReadAsync 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716471318, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716471320, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716471355, "dur": 325, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716471685, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716471687, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716471719, "dur": 248, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754351716471970, "dur": 9069, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 54336389, "ts": 1754351716495467, "dur": 24, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 8589934592, "ts": 1754351714779110, "dur": 156796, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754351714935908, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754351714935913, "dur": 1097, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 54336389, "ts": 1754351716495492, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 4294967296, "ts": 1754351714736869, "dur": 1746017, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754351714743169, "dur": 18800, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754351716482941, "dur": 4554, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754351716485540, "dur": 34, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754351716487583, "dur": 17, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 54336389, "ts": 1754351716495498, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754351714799477, "dur":4128, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754351714803619, "dur":187, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754351714803849, "dur":681, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754351714804574, "dur":80, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754351714804655, "dur":1666348, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754351716471004, "dur":216, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754351716471344, "dur":3087, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754351714806629, "dur":1664370, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754351714806721, "dur":1664325, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754351714806726, "dur":1664313, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754351714806748, "dur":1664273, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754351714806509, "dur":130178, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754351714946890, "dur":498, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":5, "ts":1754351714936688, "dur":10705, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754351714947394, "dur":1523620, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754351714806611, "dur":1664390, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754351714806665, "dur":1664443, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754351714806730, "dur":1664302, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754351714806916, "dur":762, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1754351714806478, "dur":7251, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":9, "ts":1754351714815593, "dur":1650946, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":10, "ts":1754351714806546, "dur":140850, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754351714947397, "dur":1523607, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754351714806578, "dur":1664422, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754351714806688, "dur":1664399, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754351716479591, "dur":470, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 54336389, "ts": 1754351716496035, "dur": 14444, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 54336389, "ts": 1754351716510639, "dur": 2247, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 54336389, "ts": 1754351716493489, "dur": 20313, "ph": "X", "name": "Write chrome-trace events", "args": {} },
