{ "pid": 93864, "tid": 73014444032, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971578357, "dur": 53670, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971632029, "dur": 383141, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971632043, "dur": 30, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971632078, "dur": 1236, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971633317, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971633320, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971633340, "dur": 5, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971633348, "dur": 4359, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971637717, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971637722, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971637790, "dur": 3, "ph": "X", "name": "ProcessMessages 1623", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971637794, "dur": 198, "ph": "X", "name": "ReadAsync 1623", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971637996, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638011, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638056, "dur": 3, "ph": "X", "name": "ProcessMessages 619", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638059, "dur": 63, "ph": "X", "name": "ReadAsync 619", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638128, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638161, "dur": 32, "ph": "X", "name": "ReadAsync 179", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638200, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638226, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638228, "dur": 25, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638257, "dur": 28, "ph": "X", "name": "ReadAsync 194", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638288, "dur": 1, "ph": "X", "name": "ProcessMessages 111", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638290, "dur": 23, "ph": "X", "name": "ReadAsync 111", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638316, "dur": 17, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638335, "dur": 21, "ph": "X", "name": "ReadAsync 77", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638360, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638384, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638387, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638416, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638418, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638448, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638450, "dur": 25, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638480, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638482, "dur": 29, "ph": "X", "name": "ReadAsync 163", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638514, "dur": 24, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638541, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638543, "dur": 24, "ph": "X", "name": "ReadAsync 137", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638570, "dur": 94, "ph": "X", "name": "ReadAsync 234", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638668, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638696, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638699, "dur": 22, "ph": "X", "name": "ReadAsync 195", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638724, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638751, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638753, "dur": 34, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638790, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638820, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638822, "dur": 24, "ph": "X", "name": "ReadAsync 579", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638848, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638850, "dur": 23, "ph": "X", "name": "ReadAsync 214", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638876, "dur": 22, "ph": "X", "name": "ReadAsync 136", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638901, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971638903, "dur": 150, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639057, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639091, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639094, "dur": 25, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639121, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639122, "dur": 124, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639249, "dur": 2, "ph": "X", "name": "ProcessMessages 1685", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639253, "dur": 32, "ph": "X", "name": "ReadAsync 1685", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639288, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639290, "dur": 32, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639325, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639327, "dur": 37, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639367, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639401, "dur": 26, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639430, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639432, "dur": 31, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639465, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639468, "dur": 31, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639502, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639504, "dur": 30, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639538, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639540, "dur": 35, "ph": "X", "name": "ReadAsync 441", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639578, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639584, "dur": 28, "ph": "X", "name": "ReadAsync 236", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639614, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639616, "dur": 37, "ph": "X", "name": "ReadAsync 183", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639657, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639659, "dur": 30, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639692, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639694, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639727, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639730, "dur": 28, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639763, "dur": 28, "ph": "X", "name": "ReadAsync 113", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639795, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639838, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639841, "dur": 34, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639877, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639880, "dur": 28, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639911, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639913, "dur": 33, "ph": "X", "name": "ReadAsync 376", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639948, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639950, "dur": 25, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971639979, "dur": 27, "ph": "X", "name": "ReadAsync 215", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640008, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640011, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640033, "dur": 24, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640060, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640062, "dur": 25, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640090, "dur": 101, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640194, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640196, "dur": 54, "ph": "X", "name": "ReadAsync 65", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640252, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640255, "dur": 29, "ph": "X", "name": "ReadAsync 783", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640287, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640289, "dur": 43, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640335, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640338, "dur": 29, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640370, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640372, "dur": 32, "ph": "X", "name": "ReadAsync 621", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640407, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640409, "dur": 29, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640439, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640441, "dur": 24, "ph": "X", "name": "ReadAsync 633", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640467, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640468, "dur": 41, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640514, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640543, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640545, "dur": 33, "ph": "X", "name": "ReadAsync 480", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640580, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640582, "dur": 30, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640614, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640618, "dur": 27, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640648, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640650, "dur": 28, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640680, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640682, "dur": 43, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640729, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640731, "dur": 38, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640771, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640773, "dur": 30, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640804, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640806, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640830, "dur": 26, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640859, "dur": 31, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640893, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640895, "dur": 29, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640926, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640927, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640951, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640952, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640978, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971640980, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641002, "dur": 42, "ph": "X", "name": "ReadAsync 231", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641049, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641088, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641090, "dur": 28, "ph": "X", "name": "ReadAsync 581", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641121, "dur": 25, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641149, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641151, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641175, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641177, "dur": 27, "ph": "X", "name": "ReadAsync 188", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641206, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641208, "dur": 26, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641237, "dur": 24, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641272, "dur": 27, "ph": "X", "name": "ReadAsync 195", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641300, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641302, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641329, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641353, "dur": 1, "ph": "X", "name": "ProcessMessages 152", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641355, "dur": 30, "ph": "X", "name": "ReadAsync 152", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641389, "dur": 25, "ph": "X", "name": "ReadAsync 513", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641417, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641443, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641468, "dur": 22, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641492, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641494, "dur": 24, "ph": "X", "name": "ReadAsync 215", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641522, "dur": 37, "ph": "X", "name": "ReadAsync 227", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641563, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641592, "dur": 18, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641613, "dur": 25, "ph": "X", "name": "ReadAsync 168", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641641, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641643, "dur": 43, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641689, "dur": 26, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641718, "dur": 24, "ph": "X", "name": "ReadAsync 493", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641746, "dur": 25, "ph": "X", "name": "ReadAsync 375", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641772, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641774, "dur": 30, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641807, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641809, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641837, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641839, "dur": 20, "ph": "X", "name": "ReadAsync 569", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641862, "dur": 27, "ph": "X", "name": "ReadAsync 175", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641892, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641893, "dur": 28, "ph": "X", "name": "ReadAsync 454", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641923, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641925, "dur": 28, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641955, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641957, "dur": 29, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641988, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971641990, "dur": 24, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642016, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642018, "dur": 35, "ph": "X", "name": "ReadAsync 65", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642056, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642089, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642091, "dur": 25, "ph": "X", "name": "ReadAsync 531", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642118, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642119, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642144, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642145, "dur": 32, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642180, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642182, "dur": 27, "ph": "X", "name": "ReadAsync 436", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642211, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642212, "dur": 27, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642243, "dur": 28, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642272, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642275, "dur": 28, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642305, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642307, "dur": 30, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642340, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642342, "dur": 23, "ph": "X", "name": "ReadAsync 419", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642368, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642370, "dur": 26, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642397, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642399, "dur": 26, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642428, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642429, "dur": 32, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642463, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642465, "dur": 28, "ph": "X", "name": "ReadAsync 443", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642495, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642498, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642518, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642543, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642545, "dur": 27, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642574, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642576, "dur": 28, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642606, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642609, "dur": 39, "ph": "X", "name": "ReadAsync 459", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642650, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642652, "dur": 31, "ph": "X", "name": "ReadAsync 528", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642685, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642688, "dur": 249, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642942, "dur": 31, "ph": "X", "name": "ReadAsync 85", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642976, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971642978, "dur": 31, "ph": "X", "name": "ReadAsync 436", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643012, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643014, "dur": 29, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643045, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643048, "dur": 29, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643081, "dur": 25, "ph": "X", "name": "ReadAsync 311", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643108, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643110, "dur": 29, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643142, "dur": 25, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643171, "dur": 25, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643199, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643201, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643231, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643233, "dur": 27, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643262, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643264, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643288, "dur": 27, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643317, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643319, "dur": 45, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643367, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643369, "dur": 54, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643426, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643428, "dur": 34, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643465, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643467, "dur": 33, "ph": "X", "name": "ReadAsync 654", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643503, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643505, "dur": 29, "ph": "X", "name": "ReadAsync 475", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643537, "dur": 26, "ph": "X", "name": "ReadAsync 532", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643566, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643568, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643595, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643596, "dur": 26, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643625, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643627, "dur": 33, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643663, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643665, "dur": 29, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643697, "dur": 23, "ph": "X", "name": "ReadAsync 442", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643723, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643746, "dur": 26, "ph": "X", "name": "ReadAsync 220", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643775, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643777, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643805, "dur": 22, "ph": "X", "name": "ReadAsync 217", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643830, "dur": 14, "ph": "X", "name": "ReadAsync 458", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643846, "dur": 22, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643871, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643899, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643901, "dur": 30, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643934, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643936, "dur": 34, "ph": "X", "name": "ReadAsync 544", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971643973, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644002, "dur": 31, "ph": "X", "name": "ReadAsync 145", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644036, "dur": 23, "ph": "X", "name": "ReadAsync 518", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644063, "dur": 27, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644094, "dur": 27, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644123, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644125, "dur": 33, "ph": "X", "name": "ReadAsync 452", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644161, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644164, "dur": 26, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644193, "dur": 23, "ph": "X", "name": "ReadAsync 540", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644219, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644221, "dur": 29, "ph": "X", "name": "ReadAsync 223", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644253, "dur": 30, "ph": "X", "name": "ReadAsync 523", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644286, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644287, "dur": 28, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644319, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644342, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644345, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644369, "dur": 34, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644406, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644408, "dur": 28, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644438, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644439, "dur": 33, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644475, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644476, "dur": 41, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644519, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644520, "dur": 21, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644544, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644546, "dur": 33, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644582, "dur": 23, "ph": "X", "name": "ReadAsync 518", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644609, "dur": 31, "ph": "X", "name": "ReadAsync 11", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644642, "dur": 25, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644670, "dur": 25, "ph": "X", "name": "ReadAsync 326", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644700, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644727, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644729, "dur": 24, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644755, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644757, "dur": 29, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644788, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644790, "dur": 27, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644820, "dur": 28, "ph": "X", "name": "ReadAsync 618", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644850, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644852, "dur": 30, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644884, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644886, "dur": 24, "ph": "X", "name": "ReadAsync 521", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644913, "dur": 28, "ph": "X", "name": "ReadAsync 543", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644943, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644945, "dur": 44, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644993, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971644995, "dur": 31, "ph": "X", "name": "ReadAsync 559", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645028, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645030, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645057, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645058, "dur": 95, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645156, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645158, "dur": 36, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645196, "dur": 1, "ph": "X", "name": "ProcessMessages 1076", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645199, "dur": 26, "ph": "X", "name": "ReadAsync 1076", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645228, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645230, "dur": 34, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645267, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645269, "dur": 24, "ph": "X", "name": "ReadAsync 490", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645295, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645298, "dur": 30, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645331, "dur": 38, "ph": "X", "name": "ReadAsync 575", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645373, "dur": 31, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645407, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645409, "dur": 32, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645445, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645447, "dur": 61, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645511, "dur": 31, "ph": "X", "name": "ReadAsync 558", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645546, "dur": 21, "ph": "X", "name": "ReadAsync 645", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645569, "dur": 1, "ph": "X", "name": "ProcessMessages 105", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645571, "dur": 32, "ph": "X", "name": "ReadAsync 105", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645606, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645608, "dur": 26, "ph": "X", "name": "ReadAsync 627", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645636, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645638, "dur": 32, "ph": "X", "name": "ReadAsync 274", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645674, "dur": 22, "ph": "X", "name": "ReadAsync 577", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645699, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645724, "dur": 29, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645756, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645758, "dur": 28, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645788, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645791, "dur": 30, "ph": "X", "name": "ReadAsync 439", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645824, "dur": 29, "ph": "X", "name": "ReadAsync 683", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645857, "dur": 21, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645882, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645910, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645912, "dur": 29, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645945, "dur": 20, "ph": "X", "name": "ReadAsync 715", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645967, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971645970, "dur": 26, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646002, "dur": 24, "ph": "X", "name": "ReadAsync 518", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646028, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646030, "dur": 29, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646062, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646064, "dur": 33, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646098, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646100, "dur": 63, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646167, "dur": 28, "ph": "X", "name": "ReadAsync 268", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646199, "dur": 24, "ph": "X", "name": "ReadAsync 603", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646226, "dur": 23, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646251, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646253, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646276, "dur": 22, "ph": "X", "name": "ReadAsync 109", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646301, "dur": 27, "ph": "X", "name": "ReadAsync 149", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646332, "dur": 28, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646362, "dur": 6, "ph": "X", "name": "ProcessMessages 532", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646369, "dur": 32, "ph": "X", "name": "ReadAsync 532", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646403, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646405, "dur": 28, "ph": "X", "name": "ReadAsync 680", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646437, "dur": 24, "ph": "X", "name": "ReadAsync 634", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646469, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646497, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646499, "dur": 27, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646529, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646531, "dur": 32, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646566, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646568, "dur": 29, "ph": "X", "name": "ReadAsync 563", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646599, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646601, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646630, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646663, "dur": 36, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646701, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646703, "dur": 28, "ph": "X", "name": "ReadAsync 533", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646734, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646736, "dur": 26, "ph": "X", "name": "ReadAsync 554", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646765, "dur": 29, "ph": "X", "name": "ReadAsync 377", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646797, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646799, "dur": 42, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646843, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646846, "dur": 36, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646885, "dur": 30, "ph": "X", "name": "ReadAsync 615", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646921, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646924, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646952, "dur": 28, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646982, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971646983, "dur": 24, "ph": "X", "name": "ReadAsync 646", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647010, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647012, "dur": 26, "ph": "X", "name": "ReadAsync 58", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647041, "dur": 28, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647072, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647074, "dur": 33, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647111, "dur": 23, "ph": "X", "name": "ReadAsync 496", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647136, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647138, "dur": 29, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647169, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647171, "dur": 28, "ph": "X", "name": "ReadAsync 489", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647202, "dur": 26, "ph": "X", "name": "ReadAsync 492", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647230, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647232, "dur": 24, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647257, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647260, "dur": 27, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647290, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647292, "dur": 32, "ph": "X", "name": "ReadAsync 447", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647326, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647329, "dur": 26, "ph": "X", "name": "ReadAsync 534", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647357, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647359, "dur": 29, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647391, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647393, "dur": 32, "ph": "X", "name": "ReadAsync 619", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647427, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647429, "dur": 35, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647466, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647468, "dur": 29, "ph": "X", "name": "ReadAsync 547", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647500, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647502, "dur": 33, "ph": "X", "name": "ReadAsync 578", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647538, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647540, "dur": 29, "ph": "X", "name": "ReadAsync 676", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647572, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647574, "dur": 25, "ph": "X", "name": "ReadAsync 643", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647601, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647602, "dur": 23, "ph": "X", "name": "ReadAsync 403", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647628, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647630, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647658, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647660, "dur": 27, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647690, "dur": 30, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647723, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647725, "dur": 29, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647758, "dur": 22, "ph": "X", "name": "ReadAsync 601", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647782, "dur": 1, "ph": "X", "name": "ProcessMessages 127", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647784, "dur": 28, "ph": "X", "name": "ReadAsync 127", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647814, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647816, "dur": 27, "ph": "X", "name": "ReadAsync 560", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647846, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647847, "dur": 28, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647878, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647880, "dur": 32, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647915, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647917, "dur": 30, "ph": "X", "name": "ReadAsync 489", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647950, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647952, "dur": 34, "ph": "X", "name": "ReadAsync 508", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647988, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971647990, "dur": 26, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648021, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648023, "dur": 27, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648054, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648055, "dur": 30, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648089, "dur": 25, "ph": "X", "name": "ReadAsync 601", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648117, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648119, "dur": 27, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648148, "dur": 2, "ph": "X", "name": "ProcessMessages 389", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648151, "dur": 29, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648183, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648185, "dur": 30, "ph": "X", "name": "ReadAsync 708", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648217, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648219, "dur": 31, "ph": "X", "name": "ReadAsync 502", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648253, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648255, "dur": 31, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648288, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648289, "dur": 25, "ph": "X", "name": "ReadAsync 508", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648318, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648343, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648344, "dur": 26, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648373, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648375, "dur": 23, "ph": "X", "name": "ReadAsync 376", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648401, "dur": 27, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648431, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648432, "dur": 30, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648464, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648466, "dur": 34, "ph": "X", "name": "ReadAsync 843", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648502, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648504, "dur": 29, "ph": "X", "name": "ReadAsync 608", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648537, "dur": 24, "ph": "X", "name": "ReadAsync 410", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648563, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648565, "dur": 33, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648600, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648602, "dur": 33, "ph": "X", "name": "ReadAsync 497", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648637, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648638, "dur": 28, "ph": "X", "name": "ReadAsync 550", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648669, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648671, "dur": 24, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648698, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648699, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648728, "dur": 23, "ph": "X", "name": "ReadAsync 502", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648754, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971648756, "dur": 329, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649090, "dur": 24, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649115, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649117, "dur": 20, "ph": "X", "name": "ReadAsync 687", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649140, "dur": 84, "ph": "X", "name": "ReadAsync 114", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649228, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649276, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649278, "dur": 50, "ph": "X", "name": "ReadAsync 420", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649351, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649354, "dur": 26, "ph": "X", "name": "ReadAsync 682", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649383, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649401, "dur": 218, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649623, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649655, "dur": 1, "ph": "X", "name": "ProcessMessages 1694", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649658, "dur": 10, "ph": "X", "name": "ReadAsync 1694", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649670, "dur": 18, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649689, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649705, "dur": 13, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649721, "dur": 10, "ph": "X", "name": "ReadAsync 175", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649732, "dur": 49, "ph": "X", "name": "ReadAsync 143", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649784, "dur": 11, "ph": "X", "name": "ReadAsync 23", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649799, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649801, "dur": 19, "ph": "X", "name": "ReadAsync 171", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649822, "dur": 20, "ph": "X", "name": "ReadAsync 212", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649846, "dur": 29, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649879, "dur": 25, "ph": "X", "name": "ReadAsync 180", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649907, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649909, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649936, "dur": 25, "ph": "X", "name": "ReadAsync 563", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649964, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649966, "dur": 31, "ph": "X", "name": "ReadAsync 222", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971649999, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650001, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650027, "dur": 196, "ph": "X", "name": "ReadAsync 171", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650228, "dur": 55, "ph": "X", "name": "ReadAsync 135", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650285, "dur": 2, "ph": "X", "name": "ProcessMessages 2024", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650288, "dur": 85, "ph": "X", "name": "ReadAsync 2024", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650376, "dur": 3, "ph": "X", "name": "ProcessMessages 427", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650380, "dur": 95, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650478, "dur": 2, "ph": "X", "name": "ProcessMessages 2804", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650481, "dur": 16, "ph": "X", "name": "ReadAsync 2804", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650500, "dur": 32, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650537, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650577, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650579, "dur": 26, "ph": "X", "name": "ReadAsync 701", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650608, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650610, "dur": 44, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650656, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650658, "dur": 24, "ph": "X", "name": "ReadAsync 611", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650686, "dur": 15, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650718, "dur": 24, "ph": "X", "name": "ReadAsync 220", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650745, "dur": 3, "ph": "X", "name": "ProcessMessages 715", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650749, "dur": 25, "ph": "X", "name": "ReadAsync 715", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650777, "dur": 5, "ph": "X", "name": "ProcessMessages 418", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650783, "dur": 122, "ph": "X", "name": "ReadAsync 418", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650910, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650946, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650948, "dur": 27, "ph": "X", "name": "ReadAsync 549", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650977, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971650980, "dur": 42, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651025, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651026, "dur": 29, "ph": "X", "name": "ReadAsync 609", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651057, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651059, "dur": 33, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651095, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651097, "dur": 27, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651126, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651128, "dur": 27, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651158, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651160, "dur": 28, "ph": "X", "name": "ReadAsync 449", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651192, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651194, "dur": 29, "ph": "X", "name": "ReadAsync 473", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651226, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651229, "dur": 34, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651266, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651268, "dur": 22, "ph": "X", "name": "ReadAsync 648", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651293, "dur": 25, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651320, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651321, "dur": 25, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651349, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651351, "dur": 30, "ph": "X", "name": "ReadAsync 269", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651385, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651408, "dur": 23, "ph": "X", "name": "ReadAsync 93", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651434, "dur": 1, "ph": "X", "name": "ProcessMessages 94", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651435, "dur": 46, "ph": "X", "name": "ReadAsync 94", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651486, "dur": 27, "ph": "X", "name": "ReadAsync 119", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651516, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651542, "dur": 74, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651620, "dur": 34, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651657, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651659, "dur": 72, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651734, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651736, "dur": 33, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651772, "dur": 29, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651803, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651805, "dur": 28, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651835, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651837, "dur": 22, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651861, "dur": 91, "ph": "X", "name": "ReadAsync 218", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651955, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971651957, "dur": 42, "ph": "X", "name": "ReadAsync 269", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971652002, "dur": 2, "ph": "X", "name": "ProcessMessages 1309", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971652005, "dur": 29, "ph": "X", "name": "ReadAsync 1309", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971652036, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971652038, "dur": 27, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971652069, "dur": 34, "ph": "X", "name": "ReadAsync 409", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971652105, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971652107, "dur": 29, "ph": "X", "name": "ReadAsync 149", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971652138, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971652141, "dur": 32285, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971684434, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971684439, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971684473, "dur": 441, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971684919, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971684963, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971684965, "dur": 28, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971684996, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971684998, "dur": 688, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971685689, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971685693, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971685770, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971685772, "dur": 118, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971685892, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971685894, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971685952, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971685954, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686042, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686044, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686275, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686292, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686316, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686368, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686481, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686541, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686543, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686561, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686755, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686787, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686789, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686826, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686837, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686871, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686910, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971686957, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687079, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687116, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687117, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687197, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687229, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687289, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687321, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687323, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687347, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687414, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687445, "dur": 12, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687460, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687532, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687565, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687659, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687694, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687727, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687781, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687807, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687845, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687877, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687913, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687938, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687978, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971687994, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688014, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688079, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688112, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688165, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688197, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688383, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688414, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688457, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688494, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688496, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688562, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688590, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688616, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688680, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688682, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688715, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688717, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688752, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688960, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971688993, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689050, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689126, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689164, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689166, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689230, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689257, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689286, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689366, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689395, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689397, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689425, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689557, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689596, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689694, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689724, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689785, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689795, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689831, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689833, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689910, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689939, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689967, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971689969, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690030, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690060, "dur": 90, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690155, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690188, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690306, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690343, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690378, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690429, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690491, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690530, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690575, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690607, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690644, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690751, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690754, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690824, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690851, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690853, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690952, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971690983, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691005, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691022, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691143, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691188, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691426, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691467, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691470, "dur": 116, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691592, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691633, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691685, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691736, "dur": 125, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691866, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691897, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691899, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971691932, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692010, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692085, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692087, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692119, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692295, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692332, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692333, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692359, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692502, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692519, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692571, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692655, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692689, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692720, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692752, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692754, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692802, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692867, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692908, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971692910, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693074, "dur": 21, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693126, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693171, "dur": 121, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693297, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693342, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693376, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693420, "dur": 529, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693952, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693955, "dur": 32, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693990, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971693993, "dur": 96, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694094, "dur": 200, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694297, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694301, "dur": 337, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694643, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694680, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694682, "dur": 149, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694835, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694871, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694874, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694901, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694903, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971694995, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695024, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695025, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695111, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695220, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695251, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695254, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695316, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695318, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695441, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695482, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695484, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695611, "dur": 146, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695761, "dur": 2, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971695764, "dur": 273, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971696041, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971696044, "dur": 20, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971696067, "dur": 323, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971696397, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971696420, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971696424, "dur": 464, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971696893, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971696911, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971696913, "dur": 490, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971697406, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971697408, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971697436, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971697438, "dur": 513, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971697956, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971697989, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971697992, "dur": 2839, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971700837, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971700841, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971700868, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971700871, "dur": 6153, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971707034, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971707039, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971707086, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971707091, "dur": 5934, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971713034, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971713038, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971713091, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971713095, "dur": 2859, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971715958, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971715961, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971716009, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971716011, "dur": 27293, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971743313, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971743317, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971743350, "dur": 23567, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971766931, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971766936, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971766974, "dur": 2105, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971769086, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971769090, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971769155, "dur": 35, "ph": "X", "name": "ProcessMessages 172", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971769191, "dur": 1366, "ph": "X", "name": "ReadAsync 172", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971770566, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971770569, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971770633, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971770635, "dur": 232, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971770870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971770872, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971770892, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971770907, "dur": 209, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971771123, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971771216, "dur": 779, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772002, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772039, "dur": 13, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772053, "dur": 151, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772221, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772223, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772261, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772263, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772301, "dur": 335, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772639, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772641, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772671, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772673, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772741, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772870, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772921, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971772982, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971773026, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971773028, "dur": 239, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971773272, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971773303, "dur": 87, "ph": "X", "name": "ProcessMessages 98", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971773392, "dur": 29, "ph": "X", "name": "ReadAsync 98", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971773423, "dur": 623, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774274, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774277, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774312, "dur": 13, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774326, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774355, "dur": 300, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774662, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774761, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774763, "dur": 158, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774925, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971774959, "dur": 314, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971775277, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971775338, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971775422, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971775459, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971775645, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971775671, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971775885, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971775917, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971776059, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971776099, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971776175, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971776195, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971776353, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971776438, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971776479, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971776525, "dur": 718, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971777248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971777600, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971777640, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971777642, "dur": 253, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971777899, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971777943, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778004, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778010, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778028, "dur": 248, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778282, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778320, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778323, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778391, "dur": 200, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778594, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778596, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778624, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778626, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778666, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778704, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778706, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778836, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778868, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778870, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971778890, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779018, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779021, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779056, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779116, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779146, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779402, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779428, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779458, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779507, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779524, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779644, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779662, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779835, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779867, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971779902, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780007, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780043, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780045, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780103, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780124, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780181, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780213, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780216, "dur": 79, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780298, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780300, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780318, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780423, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780478, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780511, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780512, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780599, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780621, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780689, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780718, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780719, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780749, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780782, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780929, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780960, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971780962, "dur": 138, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781104, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781138, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781140, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781216, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781284, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781287, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781340, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781367, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781369, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781428, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781459, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781490, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781518, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781654, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781692, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781772, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971781798, "dur": 405, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971782208, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971782239, "dur": 1013, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783257, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783302, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783304, "dur": 78, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783387, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783426, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783431, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783513, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783547, "dur": 375, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783925, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783931, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783966, "dur": 12, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971783980, "dur": 50, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784034, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784097, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784231, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784304, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784306, "dur": 148, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784459, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784532, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784534, "dur": 364, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784903, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971784998, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971785000, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971785031, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971785034, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971785053, "dur": 1088, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971786144, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971786146, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971786209, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971786211, "dur": 467, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971786683, "dur": 868, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971787555, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971787557, "dur": 417, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971787978, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971788033, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971788149, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971788186, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971788188, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971788228, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971788230, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971788258, "dur": 1228, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971789489, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971789491, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971789599, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971789608, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971789756, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971789792, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971789794, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971789940, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971789976, "dur": 291, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790270, "dur": 43, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790315, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790370, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790405, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790537, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790576, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790578, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790605, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790712, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790743, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790745, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790773, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971790825, "dur": 354, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791184, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791267, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791270, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791292, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791401, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791449, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791451, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791496, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791497, "dur": 122, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791624, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791683, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791685, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791707, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791718, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791783, "dur": 150, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791936, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791938, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971791975, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971792060, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971792062, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971792246, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971792357, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971792359, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971792466, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971792468, "dur": 411, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971792884, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971793020, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971793022, "dur": 715, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971793742, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971793777, "dur": 158, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971793940, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971793963, "dur": 99, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794067, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794101, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794103, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794163, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794166, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794311, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794344, "dur": 202, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794553, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794598, "dur": 163, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971794766, "dur": 388, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795157, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795159, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795188, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795189, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795281, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795370, "dur": 23, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795396, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795417, "dur": 257, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795679, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795779, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795781, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795813, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795893, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971795916, "dur": 264, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796183, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796186, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796218, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796220, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796327, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796386, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796388, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796404, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796418, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796583, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796689, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796757, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796830, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796952, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796991, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971796993, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797055, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797127, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797169, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797171, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797196, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797270, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797297, "dur": 380, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797682, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797715, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797717, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797749, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797758, "dur": 226, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971797988, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971798008, "dur": 474, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971798486, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971798530, "dur": 382, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971798921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971798925, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971798958, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971798960, "dur": 84, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799048, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799092, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799094, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799165, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799209, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799211, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799387, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799389, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799426, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799428, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799505, "dur": 159, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799670, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799705, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799707, "dur": 163, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799875, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971799902, "dur": 76, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800008, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800010, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800051, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800084, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800108, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800232, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800253, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800302, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800382, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800384, "dur": 191, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971800580, "dur": 1025, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971801609, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971801612, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971801752, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971801754, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971801798, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971801804, "dur": 314, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802127, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802132, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802155, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802203, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802241, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802275, "dur": 320, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802600, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802630, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802755, "dur": 184, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802942, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971802944, "dur": 422, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971803371, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971803432, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971803434, "dur": 274, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971803713, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971803737, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971803740, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971803976, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804109, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804112, "dur": 97, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804215, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804245, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804486, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804503, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804528, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804588, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804602, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804605, "dur": 19, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971804626, "dur": 533, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805165, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805209, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805211, "dur": 117, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805333, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805348, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805349, "dur": 12, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805364, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805411, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805418, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805449, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805451, "dur": 231, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805687, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805713, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971805715, "dur": 3666, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971809390, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971809394, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971809509, "dur": 16, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971809526, "dur": 11177, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971820722, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971820733, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971820825, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971820844, "dur": 3386, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971824243, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971824248, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971824302, "dur": 22, "ph": "X", "name": "ProcessMessages 126", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971824326, "dur": 2420, "ph": "X", "name": "ReadAsync 126", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971826755, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971826760, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971826795, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971826811, "dur": 19103, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971845926, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971845930, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971845977, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971845994, "dur": 95758, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971941762, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971941767, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971941833, "dur": 18, "ph": "X", "name": "ProcessMessages 444", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971941852, "dur": 48622, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971990483, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971990488, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971990521, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971990535, "dur": 8209, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971998752, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971998758, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971998791, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349971998794, "dur": 1626, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349972000426, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349972000429, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349972000462, "dur": 12, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349972000476, "dur": 595, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349972001076, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349972001080, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349972001113, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754349972001115, "dur": 14043, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 53599707, "ts": 1754349972016797, "dur": 3511, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 68719476736, "ts": 1754349971554725, "dur": 460494, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754349971554886, "dur": 23403, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754349972015224, "dur": 10, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754349972015235, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 53599707, "ts": 1754349972020311, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754349971334025, "dur": 2428, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754349971336457, "dur": 216558, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754349971553019, "dur": 1679, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 93864, "tid": 53599707, "ts": 1754349972020323, "dur": 5, "ph": "X", "name": "", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754349971633566, "dur":3890, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349971637473, "dur":747, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349971638310, "dur":132, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1754349971638443, "dur":637, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349971639307, "dur":215, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":0, "ts":1754349971639564, "dur":103, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":0, "ts":1754349971639671, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":0, "ts":1754349971639823, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":0, "ts":1754349971641090, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971641272, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Xml.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971641622, "dur":113, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971644213, "dur":273, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971647632, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Timeline-FeaturesChecked.txt_ygu9.info" }}
,{ "pid":12345, "tid":0, "ts":1754349971650289, "dur":327, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.StreamingModule-FeaturesChecked.txt_pydr.info" }}
,{ "pid":12345, "tid":0, "ts":1754349971650651, "dur":122, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubstanceModule-FeaturesChecked.txt_rpif.info" }}
,{ "pid":12345, "tid":0, "ts":1754349971650915, "dur":152, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754349971651267, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754349971651369, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754349971651713, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UmbraModule-FeaturesChecked.txt_cv7q.info" }}
,{ "pid":12345, "tid":0, "ts":1754349971652308, "dur":153, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754349971652985, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971653099, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":0, "ts":1754349971653215, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":0, "ts":1754349971639117, "dur":14554, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349971653682, "dur":348564, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349972002253, "dur":108, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349972002578, "dur":3234, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754349971640780, "dur":13009, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971653796, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971654554, "dur":841, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971655396, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971656025, "dur":1085, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971657110, "dur":712, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971657823, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971658511, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971659293, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971660010, "dur":868, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971660879, "dur":962, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971661841, "dur":1147, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971662989, "dur":807, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971663796, "dur":738, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971664534, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971665208, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971665964, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971666651, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971667343, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971668093, "dur":1046, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971669140, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971653796, "dur":15943, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971670855, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Drawing.Design.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971673452, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Services.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971674405, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.OracleClient.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971675937, "dur":537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971669739, "dur":8422, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971678161, "dur":2839, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971681001, "dur":918, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971681001, "dur":3159, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971684160, "dur":1567, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971685743, "dur":750, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971686502, "dur":904, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971687413, "dur":465, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971687887, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971688455, "dur":735, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971689198, "dur":1019, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971690242, "dur":658, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971690907, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971691563, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971692225, "dur":624, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971692854, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971693573, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971694262, "dur":744, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971695010, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971695205, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971695368, "dur":200, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":1, "ts":1754349971695569, "dur":957, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971697792, "dur":72886, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets2.assets" }}
,{ "pid":12345, "tid":1, "ts":1754349971773512, "dur":429, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971774393, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971774736, "dur":1149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971776989, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754349971777040, "dur":650, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971779548, "dur":268, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971779830, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971779988, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971780210, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971780515, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971781992, "dur":249, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971782280, "dur":469, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971784092, "dur":955, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971786059, "dur":516, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971788169, "dur":1507, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971791601, "dur":430, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971792041, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971792521, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971792942, "dur":357, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971793337, "dur":917, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971795289, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971795604, "dur":227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971795838, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971796018, "dur":862, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971796932, "dur":867, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971798839, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971800568, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971800789, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971800929, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971801048, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971801172, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971801367, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971801664, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971804336, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971806958, "dur":40459, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":1, "ts":1754349971847571, "dur":154557, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971640512, "dur":13211, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971653731, "dur":770, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971654502, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971655193, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971655871, "dur":887, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971657233, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971657938, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971658620, "dur":754, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971659374, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971660035, "dur":964, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971660999, "dur":897, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971661896, "dur":1029, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971662925, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971663609, "dur":792, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971664401, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971665122, "dur":709, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971665831, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971666468, "dur":743, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971667211, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971667947, "dur":824, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971653731, "dur":15040, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971668771, "dur":880, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.TvOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971668771, "dur":5248, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971679432, "dur":556, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971679988, "dur":1537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971681525, "dur":789, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971674019, "dur":8295, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971682315, "dur":380, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971682695, "dur":3028, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971685738, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971686478, "dur":946, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971687459, "dur":761, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971688230, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971688952, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971689621, "dur":479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971690108, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971690817, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971691507, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971692175, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971692732, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971693431, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971694105, "dur":89, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt_p13n.info" }}
,{ "pid":12345, "tid":2, "ts":1754349971694196, "dur":685, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971694887, "dur":439, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971695330, "dur":263, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":2, "ts":1754349971695594, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971696326, "dur":777, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971697104, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971697845, "dur":72723, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":2, "ts":1754349971771355, "dur":1207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971773958, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971775387, "dur":291, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971776902, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971779416, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971781144, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterInputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754349971781202, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971782445, "dur":181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971782665, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971785114, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971785771, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971788908, "dur":809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971791658, "dur":489, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971792154, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971792649, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971793148, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971794991, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971795178, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971795463, "dur":754, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971796226, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971796407, "dur":799, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971798233, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971800266, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971801618, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971801842, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971802419, "dur":1329, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971805061, "dur":1055, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971807201, "dur":17617, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":2, "ts":1754349971826198, "dur":175988, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971640518, "dur":13213, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971653739, "dur":841, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971654580, "dur":746, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971655326, "dur":664, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971655990, "dur":897, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971656887, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971657456, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971658090, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971658779, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971659471, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971660188, "dur":1218, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971661406, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971662181, "dur":895, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971663077, "dur":726, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971663804, "dur":794, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971664598, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971665348, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971666102, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971666790, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971667459, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971668200, "dur":915, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":15377, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Routing.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Extensions.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Extensions.Design.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.DynamicData.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":8360, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":503, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971680166, "dur":2002, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971677476, "dur":7268, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971684745, "dur":969, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971685735, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971686410, "dur":997, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971687420, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971688052, "dur":835, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971688893, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971689540, "dur":955, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971690503, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971691095, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971691694, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971692366, "dur":756, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971693128, "dur":634, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971693772, "dur":845, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971694623, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971695275, "dur":418, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":3, "ts":1754349971695693, "dur":730, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971696427, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":3, "ts":1754349971696599, "dur":253, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971696886, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971769498, "dur":2812, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":3, "ts":1754349971772460, "dur":732, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971774236, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971774466, "dur":301, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971774776, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971774959, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971776531, "dur":427, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971778192, "dur":1320, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971779555, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971781046, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.LocalizationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754349971781097, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971782392, "dur":485, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971784168, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971784774, "dur":907, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971785727, "dur":778, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971788900, "dur":760, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971791240, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971791860, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971792469, "dur":1039, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971793528, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754349971793689, "dur":724, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971795456, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestTextureModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754349971795574, "dur":1227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971797683, "dur":196, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971797887, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971798254, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754349971798469, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971800366, "dur":390, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971801679, "dur":1522, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971804573, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971824693, "dur":57, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971806682, "dur":18086, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0.resS" }}
,{ "pid":12345, "tid":3, "ts":1754349971826685, "dur":175316, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971640469, "dur":13223, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971659325, "dur":491, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":4, "ts":1754349971659817, "dur":1911, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":4, "ts":1754349971661728, "dur":18237, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":4, "ts":1754349971679966, "dur":303, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":4, "ts":1754349971680269, "dur":3928, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":4, "ts":1754349971684198, "dur":324, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":4, "ts":1754349971684523, "dur":468, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1754349971684991, "dur":472, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":4, "ts":1754349971685463, "dur":135, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":4, "ts":1754349971685598, "dur":90, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":4, "ts":1754349971653701, "dur":32006, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971685718, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":4, "ts":1754349971685786, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971686212, "dur":769, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971686986, "dur":167, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687155, "dur":140, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687300, "dur":160, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687465, "dur":136, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687606, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687787, "dur":369, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971688157, "dur":467, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971688626, "dur":142, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971688772, "dur":837, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971689610, "dur":206, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971689818, "dur":368, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971690188, "dur":543, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971690733, "dur":202, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971690936, "dur":236, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971691178, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971691399, "dur":225, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971691626, "dur":190, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971691818, "dur":196, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971692017, "dur":425, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971692444, "dur":806, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971693252, "dur":146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971693404, "dur":274, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971693685, "dur":227, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971693918, "dur":344, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971694264, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971694454, "dur":2212, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971696672, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971696933, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971697190, "dur":559, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971697751, "dur":320, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971698073, "dur":246, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971698325, "dur":1079, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971699406, "dur":497, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971699905, "dur":149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971700056, "dur":350, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971701057, "dur":149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":4, "ts":1754349971701207, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":4, "ts":1754349971701357, "dur":153, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":4, "ts":1754349971701511, "dur":248, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":4, "ts":1754349971685939, "dur":16179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":4, "ts":1754349971702119, "dur":15258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971729456, "dur":15047, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":4, "ts":1754349971744504, "dur":289, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971745834, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971745938, "dur":22457, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971768419, "dur":9892, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971778322, "dur":1214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971779544, "dur":206, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971779757, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971779992, "dur":337, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971781221, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971781377, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971781538, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971781713, "dur":280, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971782017, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971782232, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971782453, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971782659, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971785004, "dur":307, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971786300, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971786443, "dur":726, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971788802, "dur":911, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971791487, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971791890, "dur":443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971792339, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971792699, "dur":527, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971793267, "dur":608, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971794961, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971796059, "dur":1159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971798280, "dur":369, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971799830, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971801382, "dur":366, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971801758, "dur":343, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971802150, "dur":1521, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971804514, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971810880, "dur":86, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971806174, "dur":4800, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1" }}
,{ "pid":12345, "tid":4, "ts":1754349971811099, "dur":191083, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971640506, "dur":13209, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971653723, "dur":722, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971654445, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971655123, "dur":709, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971655833, "dur":949, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971657265, "dur":737, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971658002, "dur":729, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971658732, "dur":770, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971659503, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971660182, "dur":1163, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971661345, "dur":789, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971662135, "dur":1045, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":5, "ts":1754349971663180, "dur":676, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971663857, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971664636, "dur":716, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971665352, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971665994, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971666712, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971667417, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971668138, "dur":871, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971653723, "dur":15286, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971671278, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.TinyProfiler2.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971669010, "dur":3431, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971674837, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971677649, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971678165, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971672442, "dur":7059, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971679501, "dur":4178, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971683679, "dur":2105, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971685786, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971686526, "dur":933, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971687465, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971688102, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971688638, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971689389, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971689995, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971690646, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971691247, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971691898, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971692495, "dur":448, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971692952, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971693614, "dur":697, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971694320, "dur":738, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971695062, "dur":227, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":5, "ts":1754349971695290, "dur":177, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971695471, "dur":847, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":5, "ts":1754349971696319, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971697933, "dur":294077, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS" }}
,{ "pid":12345, "tid":5, "ts":1754349971992125, "dur":10009, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971640814, "dur":12983, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971653805, "dur":833, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971654639, "dur":745, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971655384, "dur":703, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971656088, "dur":1098, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971657187, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971657929, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971658585, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971659335, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971660076, "dur":1140, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971661216, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971662011, "dur":917, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971662928, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971663667, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971664497, "dur":702, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971665199, "dur":761, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971665961, "dur":733, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971666694, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971667373, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971668146, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971668927, "dur":957, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971653805, "dur":16079, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971671145, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.C5.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971673017, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971674311, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Tasks.v4.0.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971674962, "dur":565, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Framework.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971675967, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\ICSharpCode.SharpZipLib.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971669884, "dur":8338, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971680628, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.UnityWebRequestWWWModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971678222, "dur":4325, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971682547, "dur":3165, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971685735, "dur":686, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971686433, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971687296, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971687820, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971688497, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971689235, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971689923, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971690592, "dur":627, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971691236, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971691835, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971692492, "dur":595, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971693095, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971693768, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971694402, "dur":687, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971695093, "dur":271, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":6, "ts":1754349971695365, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971695497, "dur":851, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":6, "ts":1754349971696349, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971698431, "dur":76347, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets" }}
,{ "pid":12345, "tid":6, "ts":1754349971774927, "dur":1271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971777338, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971779969, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971780144, "dur":507, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971780683, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971782153, "dur":321, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971782480, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Numerics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754349971782624, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971784851, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971786588, "dur":1073, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971790692, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971792001, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971792448, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971793003, "dur":81, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971793117, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971794969, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971797203, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971797715, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971797973, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971798286, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971798444, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971798618, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971800564, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971800779, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971803545, "dur":736, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971805182, "dur":872, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971806915, "dur":21353, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":6, "ts":1754349971828391, "dur":173643, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971640842, "dur":12978, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971653826, "dur":798, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971654624, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971655380, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971656031, "dur":895, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971656926, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971657635, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971658293, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971658938, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971659565, "dur":699, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971660265, "dur":1208, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971661473, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971662151, "dur":817, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971662969, "dur":842, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971663812, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971664569, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971665220, "dur":743, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971665963, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971666650, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971667352, "dur":788, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971668140, "dur":971, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971653826, "dur":15285, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971669825, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\mscorlib.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971673062, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971674451, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xaml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971676201, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Windows.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971669112, "dur":8012, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971678068, "dur":853, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971680107, "dur":1435, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971681542, "dur":955, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971677125, "dur":7549, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971684675, "dur":1072, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971685752, "dur":767, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971686526, "dur":849, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971687380, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971687999, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971688583, "dur":732, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971689331, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971689999, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971690670, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971691325, "dur":637, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971691969, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971692555, "dur":675, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971693236, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971693861, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971694614, "dur":603, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971695221, "dur":337, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":7, "ts":1754349971695559, "dur":733, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971696300, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":7, "ts":1754349971696433, "dur":281, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971698884, "dur":74405, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":7, "ts":1754349971773501, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971773753, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971774988, "dur":1202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971777386, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971780070, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971780381, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971781957, "dur":301, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971782300, "dur":457, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971784155, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971784306, "dur":369, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971784682, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971785388, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971787373, "dur":817, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971790726, "dur":556, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971792350, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971792777, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971793427, "dur":971, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971795575, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971795800, "dur":1060, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971796892, "dur":1039, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971799091, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualEffectGraph.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754349971799143, "dur":877, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971801231, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971801385, "dur":377, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971801773, "dur":1104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971802907, "dur":884, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971804942, "dur":1021, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971807274, "dur":194780, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971640519, "dur":13221, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971653748, "dur":771, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971654519, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971655224, "dur":727, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971655951, "dur":866, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971656817, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971657429, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971658006, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971658720, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971659408, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971660055, "dur":1037, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971661092, "dur":843, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971661936, "dur":1182, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971663119, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971663829, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971664587, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971665368, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971666042, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971666736, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971667433, "dur":733, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971668166, "dur":1173, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":15593, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Routing.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.Formatters.Soap.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.DurableInstancing.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":504, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Caching.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971676205, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Windows.Threading.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971669340, "dur":8634, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971677975, "dur":918, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.AppContext.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971683127, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971677974, "dur":5911, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971683885, "dur":1864, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971685751, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971686435, "dur":746, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971687197, "dur":965, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971688187, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971688739, "dur":715, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971689462, "dur":540, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971690009, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971690677, "dur":675, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971691357, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971692014, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971692687, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971693402, "dur":54, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Timeline-FeaturesChecked.txt_ygu9.info" }}
,{ "pid":12345, "tid":8, "ts":1754349971693460, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971694056, "dur":764, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971694828, "dur":504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971695356, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":8, "ts":1754349971695523, "dur":841, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971696368, "dur":785, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971697154, "dur":272, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971697866, "dur":72694, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets" }}
,{ "pid":12345, "tid":8, "ts":1754349971772437, "dur":944, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971774220, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971774368, "dur":304, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971774682, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971774858, "dur":502, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971776337, "dur":471, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971777852, "dur":822, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971780496, "dur":677, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971782232, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971782536, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971784793, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971785417, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971788372, "dur":1132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971791745, "dur":485, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971792261, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971793031, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971794544, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971796006, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971796368, "dur":843, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971798240, "dur":451, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971799927, "dur":501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971801400, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971801800, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971802530, "dur":1121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971804687, "dur":1058, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971806875, "dur":18501, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0" }}
,{ "pid":12345, "tid":8, "ts":1754349971826250, "dur":175868, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971640530, "dur":13234, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971653772, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971654529, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971655239, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971655950, "dur":869, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971656819, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971657382, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971658009, "dur":733, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971658742, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971659437, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971660144, "dur":1151, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971661295, "dur":797, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971662092, "dur":920, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971663012, "dur":751, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971663763, "dur":828, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971664591, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971665230, "dur":737, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971665968, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971666636, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971667297, "dur":752, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971668049, "dur":1373, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971653772, "dur":15651, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971670141, "dur":646, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Interfaces.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971670787, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Experimental.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971671295, "dur":524, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Debugger.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971673864, "dur":500, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.WebRequest.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971674364, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.Formatting.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971676106, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Json.Microsoft.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971669423, "dur":8490, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971679635, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971680263, "dur":1460, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971681723, "dur":788, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Data.SqlClient.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971677913, "dur":6815, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971684728, "dur":991, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971685735, "dur":726, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971686467, "dur":882, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971687353, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971687513, "dur":654, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971688167, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_uyte.info" }}
,{ "pid":12345, "tid":9, "ts":1754349971688236, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971688832, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971689535, "dur":732, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971690281, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971690952, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971691581, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971692255, "dur":947, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971693210, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971693898, "dur":791, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971694694, "dur":483, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971695181, "dur":267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":9, "ts":1754349971695448, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971695621, "dur":336, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":9, "ts":1754349971695964, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971696404, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":9, "ts":1754349971696559, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971696989, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971698158, "dur":72323, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":9, "ts":1754349971770619, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971770808, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971770957, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971771139, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971772989, "dur":820, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971775282, "dur":279, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971776714, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971778211, "dur":1209, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971779440, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754349971779621, "dur":499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971781139, "dur":563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971782454, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971784595, "dur":971, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971785606, "dur":905, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971788764, "dur":1005, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971792099, "dur":449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971792554, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971793256, "dur":943, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971794961, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971795147, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971795684, "dur":326, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971796022, "dur":292, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971796354, "dur":935, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971798600, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971800356, "dur":506, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971801861, "dur":503, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971802403, "dur":1313, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971821154, "dur":83, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971805533, "dur":15755, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":9, "ts":1754349971821474, "dur":180744, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971640536, "dur":13246, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971653789, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971654523, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971655213, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971655921, "dur":971, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971656893, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971657545, "dur":683, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971658228, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971658890, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971659543, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971660238, "dur":1205, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971661443, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971662197, "dur":811, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971663008, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971663729, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971664520, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971665228, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971665949, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971666634, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971667302, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971668041, "dur":824, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971653789, "dur":15076, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971668866, "dur":745, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Analytics.deps.json" }}
,{ "pid":12345, "tid":10, "ts":1754349971668865, "dur":3776, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971672641, "dur":5970, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971678611, "dur":4327, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971682938, "dur":2771, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971685718, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":10, "ts":1754349971685775, "dur":724, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971686516, "dur":163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349971686692, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":10, "ts":1754349971686974, "dur":746, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971687727, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971687908, "dur":410, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971688319, "dur":460, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971688782, "dur":840, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971689624, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971689811, "dur":361, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971690178, "dur":538, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971690718, "dur":209, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971690929, "dur":250, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971691180, "dur":199, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971691397, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971691623, "dur":187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971691815, "dur":192, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971692013, "dur":449, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971692464, "dur":905, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971693374, "dur":279, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971693655, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971693834, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971693976, "dur":282, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971694260, "dur":230, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971694492, "dur":2302, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971696796, "dur":150, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971696948, "dur":219, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971697170, "dur":566, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971697738, "dur":323, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971698066, "dur":267, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971698334, "dur":684, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971699021, "dur":509, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971699532, "dur":474, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971700008, "dur":449, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971686505, "dur":14703, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349971702225, "dur":115, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971703322, "dur":239758, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349971999968, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349971999866, "dur":360, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349972000251, "dur":1712, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":11, "ts":1754349971640491, "dur":13209, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971654114, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\WindowsBase.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971654772, "dur":724, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":11, "ts":1754349971655497, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":11, "ts":1754349971656114, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":11, "ts":1754349971657007, "dur":904, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971657911, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":11, "ts":1754349971658533, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971659257, "dur":714, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971659972, "dur":945, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":11, "ts":1754349971660918, "dur":948, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971661866, "dur":1013, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971662879, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971663538, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971664244, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971664893, "dur":719, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971665612, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971666167, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971653713, "dur":13116, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971666830, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":11, "ts":1754349971667487, "dur":760, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":11, "ts":1754349971668247, "dur":1424, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.exe" }}
,{ "pid":12345, "tid":11, "ts":1754349971672871, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.VisualStudio.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971666830, "dur":6897, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971674658, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Cryptography.Encryption.ECDsa.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971677967, "dur":841, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971680485, "dur":1201, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971681686, "dur":798, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971673728, "dur":9122, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971682850, "dur":2863, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971685735, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971686409, "dur":786, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971687205, "dur":644, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971687855, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971688362, "dur":701, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971689071, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971689699, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971690239, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971690927, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971691578, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971692279, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971692889, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971693561, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971694125, "dur":71, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Recorder.Base-FeaturesChecked.txt_23aq.info" }}
,{ "pid":12345, "tid":11, "ts":1754349971694199, "dur":752, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971694959, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971695099, "dur":468, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":11, "ts":1754349971695567, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971695710, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":11, "ts":1754349971695954, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971696129, "dur":278, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":11, "ts":1754349971696408, "dur":333, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971699475, "dur":75723, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":11, "ts":1754349971775534, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971777017, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971778867, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971779542, "dur":202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971779754, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971779933, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971780102, "dur":331, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781227, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781419, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781570, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781706, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781942, "dur":366, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971783241, "dur":499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971785294, "dur":399, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971786648, "dur":808, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971788887, "dur":776, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971791309, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971791462, "dur":325, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971791797, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971792296, "dur":401, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971792705, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971793268, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971794972, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971797217, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971797580, "dur":251, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971797848, "dur":268, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971798125, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971798331, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971799928, "dur":503, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971801334, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971801523, "dur":305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971801838, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971802322, "dur":1403, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971804538, "dur":422, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971806140, "dur":4768, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1.resS" }}
,{ "pid":12345, "tid":11, "ts":1754349971811020, "dur":191193, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971640885, "dur":12949, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971653834, "dur":770, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971654604, "dur":752, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971655357, "dur":716, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971656074, "dur":1167, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971657241, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971657941, "dur":746, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_7.0.1323.51816.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971658687, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971659395, "dur":702, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971660098, "dur":1183, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971661281, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971662020, "dur":919, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971662939, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971663697, "dur":776, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971664474, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971665146, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971665797, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971666592, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971667259, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971668016, "dur":965, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971668981, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":12, "ts":1754349971653834, "dur":16041, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971671095, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Novell.Directory.Ldap.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971671610, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.XBuild.Tasks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971673115, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Simd.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971674493, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Profiler.Log.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971675899, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Options.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971669875, "dur":8399, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971681060, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971678275, "dur":4667, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971682943, "dur":2768, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971685737, "dur":680, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971686428, "dur":906, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971687351, "dur":813, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971688240, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971688836, "dur":707, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971689553, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971690149, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971690772, "dur":672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971691452, "dur":652, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971692111, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971692760, "dur":780, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971693547, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971694236, "dur":776, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971695017, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":12, "ts":1754349971695209, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971695356, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":12, "ts":1754349971695537, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971695717, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":12, "ts":1754349971695877, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971696076, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":12, "ts":1754349971696246, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971696380, "dur":221, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971696601, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971708517, "dur":67256, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":12, "ts":1754349971775906, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754349971775961, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971777415, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971780103, "dur":303, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971780445, "dur":525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971781932, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971782112, "dur":310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971782465, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971784487, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971784622, "dur":297, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971784957, "dur":414, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971786545, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971789626, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.StreamingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754349971789694, "dur":1312, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971792234, "dur":415, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971792661, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971793293, "dur":945, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971795242, "dur":351, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971795600, "dur":1120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971796754, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971798649, "dur":501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971800438, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971800621, "dur":523, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971803410, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971805198, "dur":814, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971807248, "dur":194869, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349972012205, "dur":3269, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 53599707, "ts": 1754349972020361, "dur": 6124, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 53599707, "ts": 1754349972026645, "dur": 1329, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 53599707, "ts": 1754349972016793, "dur": 11235, "ph": "X", "name": "Write chrome-trace events", "args": {} },
