{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754352449110526, "dur": 1955, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754352449112483, "dur": 35783, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754352449148268, "dur": 1291, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 54631034, "ts": 1754352449177271, "dur": 13, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449110492, "dur": 36077, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449146571, "dur": 26702, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449146580, "dur": 31, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449146615, "dur": 759, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449147380, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449147400, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449147405, "dur": 3916, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449151326, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449151328, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449151363, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449151366, "dur": 1376, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449152748, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449152794, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449152796, "dur": 6695, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449159500, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449159504, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449159533, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449159535, "dur": 3295, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449162836, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449162839, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449162880, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754352449162882, "dur": 10384, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 54631034, "ts": 1754352449177286, "dur": 37, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 47244640256, "ts": 1754352449110447, "dur": 39126, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ts": 1754352449149574, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ts": 1754352449149575, "dur": 33, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 54631034, "ts": 1754352449177325, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 42949672960, "ts": 1754352449100033, "dur": 73274, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754352449100138, "dur": 10269, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754352449173310, "dur": 7, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754352449173319, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 54631034, "ts": 1754352449177333, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754352449146458, "dur":3752, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352449150243, "dur":236, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352449150519, "dur":617, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352449151173, "dur":99, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352449151273, "dur":11137, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352449162411, "dur":237, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352449162805, "dur":3506, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754352449152742, "dur":9878, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352449152765, "dur":9627, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352449152779, "dur":9611, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352449152698, "dur":6626, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":4, "ts":1754352449159325, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352449159456, "dur":2933, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352449152808, "dur":9601, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352449152747, "dur":9844, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352449152751, "dur":9812, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352449152784, "dur":9705, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352449152803, "dur":9619, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352449161858, "dur":508, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":10, "ts":1754352449152773, "dur":9598, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352449152734, "dur":9681, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352449152752, "dur":9781, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352449172277, "dur":442, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 54631034, "ts": 1754352449177376, "dur": 7867, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 54631034, "ts": 1754352449185399, "dur": 644, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 54631034, "ts": 1754352449177266, "dur": 8812, "ph": "X", "name": "Write chrome-trace events", "args": {} },
