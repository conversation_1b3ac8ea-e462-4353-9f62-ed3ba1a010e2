{ "pid": 241644, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 241644, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 241644, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 241644, "tid": 1, "ts": 1754350985154751, "dur": 405811, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 241644, "tid": 1, "ts": 1754350985156901, "dur": 63401, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 241644, "tid": 1, "ts": 1754350985165487, "dur": 51242, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 241644, "tid": 1, "ts": 1754350985249672, "dur": 14356, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 241644, "tid": 1, "ts": 1754350985265188, "dur": 80700, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 241644, "tid": 1, "ts": 1754350985345939, "dur": 16935, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 241644, "tid": 1, "ts": 1754350985362895, "dur": 190283, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 241644, "tid": 1, "ts": 1754350985558398, "dur": 1979, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 241644, "tid": 1, "ts": 1754350985560564, "dur": 469, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 241644, "tid": 1, "ts": 1754350985568696, "dur": 3338, "ph": "X", "name": "", "args": {} },
{ "pid": 241644, "tid": 1, "ts": 1754350985568071, "dur": 4483, "ph": "X", "name": "Write chrome-trace events", "args": {} },
