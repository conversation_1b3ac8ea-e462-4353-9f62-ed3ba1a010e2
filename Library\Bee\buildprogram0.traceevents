{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1754350994133900, "dur": 296693, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994135017, "dur": 47184, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994190986, "dur": 207424, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994191728, "dur": 164331, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994192259, "dur": 47085, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994239695, "dur": 640, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994242367, "dur": 111091, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994405099, "dur": 1069, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994406169, "dur": 24416, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994407291, "dur": 19554, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994437298, "dur": 1354, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754350994436765, "dur": 2076, "ph": "X", "name": "Write chrome-trace events", "args": {} },
