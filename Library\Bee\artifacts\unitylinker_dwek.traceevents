{ "pid": 91492, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 91492, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 91492, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 91492, "tid": 1, "ts": 1754352118767395, "dur": 391347, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 91492, "tid": 1, "ts": 1754352118769444, "dur": 51296, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 91492, "tid": 1, "ts": 1754352118776369, "dur": 42542, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 91492, "tid": 1, "ts": 1754352118845883, "dur": 13609, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 91492, "tid": 1, "ts": 1754352118860450, "dur": 78764, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 91492, "tid": 1, "ts": 1754352118939387, "dur": 16990, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 91492, "tid": 1, "ts": 1754352118956391, "dur": 193332, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 91492, "tid": 1, "ts": 1754352119155797, "dur": 2765, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 91492, "tid": 1, "ts": 1754352119158744, "dur": 474, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 91492, "tid": 1, "ts": 1754352119167832, "dur": 2377, "ph": "X", "name": "", "args": {} },
{ "pid": 91492, "tid": 1, "ts": 1754352119167217, "dur": 3382, "ph": "X", "name": "Write chrome-trace events", "args": {} },
