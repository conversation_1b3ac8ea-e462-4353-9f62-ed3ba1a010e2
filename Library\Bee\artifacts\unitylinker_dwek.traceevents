{ "pid": 252660, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 252660, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 252660, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 252660, "tid": 1, "ts": 1754349717473681, "dur": 405244, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 252660, "tid": 1, "ts": 1754349717475688, "dur": 49380, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 252660, "tid": 1, "ts": 1754349717482637, "dur": 40867, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 252660, "tid": 1, "ts": 1754349717550092, "dur": 14148, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 252660, "tid": 1, "ts": 1754349717565207, "dur": 82057, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 252660, "tid": 1, "ts": 1754349717647303, "dur": 20156, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 252660, "tid": 1, "ts": 1754349717667480, "dur": 202709, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 252660, "tid": 1, "ts": 1754349717876288, "dur": 2438, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 252660, "tid": 1, "ts": 1754349717878927, "dur": 458, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 252660, "tid": 1, "ts": 1754349717887281, "dur": 2580, "ph": "X", "name": "", "args": {} },
{ "pid": 252660, "tid": 1, "ts": 1754349717886167, "dur": 4083, "ph": "X", "name": "Write chrome-trace events", "args": {} },
