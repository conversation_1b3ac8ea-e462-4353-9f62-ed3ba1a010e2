{ "pid": 93864, "tid": 73014444032, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469102493, "dur": 34122, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469136616, "dur": 2991776, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469136627, "dur": 25, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469136654, "dur": 840, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469137499, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469137502, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469137525, "dur": 5, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469137532, "dur": 4172, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141714, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141718, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141759, "dur": 2, "ph": "X", "name": "ProcessMessages 972", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141762, "dur": 38, "ph": "X", "name": "ReadAsync 972", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141804, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141806, "dur": 41, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141849, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141851, "dur": 43, "ph": "X", "name": "ReadAsync 554", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141898, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141900, "dur": 43, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141946, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141948, "dur": 39, "ph": "X", "name": "ReadAsync 447", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141990, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469141992, "dur": 30, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142026, "dur": 28, "ph": "X", "name": "ReadAsync 241", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142056, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142059, "dur": 33, "ph": "X", "name": "ReadAsync 266", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142094, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142097, "dur": 19, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142119, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142122, "dur": 25, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142151, "dur": 33, "ph": "X", "name": "ReadAsync 234", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142187, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142190, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142219, "dur": 83, "ph": "X", "name": "ReadAsync 137", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142307, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142344, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142346, "dur": 38, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142386, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142388, "dur": 19, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142411, "dur": 24, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142438, "dur": 28, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142469, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142471, "dur": 35, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142508, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142509, "dur": 26, "ph": "X", "name": "ReadAsync 519", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142539, "dur": 131, "ph": "X", "name": "ReadAsync 67", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142674, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142706, "dur": 29, "ph": "X", "name": "ReadAsync 491", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142737, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142739, "dur": 30, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142772, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142774, "dur": 33, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142809, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142811, "dur": 31, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142844, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142845, "dur": 27, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142874, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142876, "dur": 29, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142907, "dur": 29, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142939, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142940, "dur": 47, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469142992, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143018, "dur": 24, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143046, "dur": 25, "ph": "X", "name": "ReadAsync 156", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143074, "dur": 21, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143099, "dur": 26, "ph": "X", "name": "ReadAsync 137", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143129, "dur": 23, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143154, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143157, "dur": 23, "ph": "X", "name": "ReadAsync 263", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143182, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143183, "dur": 39, "ph": "X", "name": "ReadAsync 305", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143225, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143226, "dur": 37, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143267, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143269, "dur": 43, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143315, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143317, "dur": 65, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143386, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143387, "dur": 27, "ph": "X", "name": "ReadAsync 305", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143419, "dur": 32, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143453, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143455, "dur": 34, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143492, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143494, "dur": 16, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143513, "dur": 55, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143571, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143619, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143621, "dur": 36, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143661, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143681, "dur": 17, "ph": "X", "name": "ReadAsync 101", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143701, "dur": 11, "ph": "X", "name": "ReadAsync 10", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143716, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143743, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143745, "dur": 110, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143859, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143861, "dur": 46, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143910, "dur": 2, "ph": "X", "name": "ProcessMessages 1600", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143913, "dur": 36, "ph": "X", "name": "ReadAsync 1600", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143951, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143953, "dur": 35, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143991, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469143993, "dur": 31, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144029, "dur": 32, "ph": "X", "name": "ReadAsync 208", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144063, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144064, "dur": 34, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144101, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144103, "dur": 31, "ph": "X", "name": "ReadAsync 375", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144137, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144139, "dur": 30, "ph": "X", "name": "ReadAsync 563", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144172, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144174, "dur": 30, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144207, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144245, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144247, "dur": 30, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144281, "dur": 48, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144332, "dur": 33, "ph": "X", "name": "ReadAsync 257", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144368, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144371, "dur": 35, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144408, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144410, "dur": 34, "ph": "X", "name": "ReadAsync 590", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144447, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144449, "dur": 35, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144486, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144488, "dur": 31, "ph": "X", "name": "ReadAsync 524", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144522, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144525, "dur": 38, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144566, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144569, "dur": 33, "ph": "X", "name": "ReadAsync 531", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144604, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144606, "dur": 28, "ph": "X", "name": "ReadAsync 556", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144636, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144638, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144667, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144668, "dur": 27, "ph": "X", "name": "ReadAsync 494", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144698, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144699, "dur": 35, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144736, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144737, "dur": 28, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144767, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144770, "dur": 25, "ph": "X", "name": "ReadAsync 446", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144798, "dur": 26, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144827, "dur": 26, "ph": "X", "name": "ReadAsync 512", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144855, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144857, "dur": 26, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144886, "dur": 21, "ph": "X", "name": "ReadAsync 198", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144910, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144935, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144960, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144962, "dur": 26, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144990, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469144992, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145018, "dur": 163, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145185, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145187, "dur": 38, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145228, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145230, "dur": 31, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145263, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145264, "dur": 27, "ph": "X", "name": "ReadAsync 655", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145293, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145295, "dur": 25, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145323, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145350, "dur": 21, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145374, "dur": 23, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145400, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145428, "dur": 23, "ph": "X", "name": "ReadAsync 497", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145452, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145454, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145481, "dur": 25, "ph": "X", "name": "ReadAsync 495", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145510, "dur": 25, "ph": "X", "name": "ReadAsync 502", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145536, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145537, "dur": 31, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145571, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145573, "dur": 24, "ph": "X", "name": "ReadAsync 645", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145600, "dur": 107, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145711, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145746, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145748, "dur": 30, "ph": "X", "name": "ReadAsync 714", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145781, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145783, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145813, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145836, "dur": 22, "ph": "X", "name": "ReadAsync 70", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145860, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145862, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145893, "dur": 26, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145921, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145922, "dur": 23, "ph": "X", "name": "ReadAsync 690", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145949, "dur": 29, "ph": "X", "name": "ReadAsync 184", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145980, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469145982, "dur": 23, "ph": "X", "name": "ReadAsync 687", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146008, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146034, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146058, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146081, "dur": 20, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146104, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146130, "dur": 20, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146154, "dur": 21, "ph": "X", "name": "ReadAsync 377", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146178, "dur": 29, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146210, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146212, "dur": 32, "ph": "X", "name": "ReadAsync 77", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146247, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146249, "dur": 40, "ph": "X", "name": "ReadAsync 650", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146294, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146333, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146334, "dur": 30, "ph": "X", "name": "ReadAsync 611", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146367, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146370, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146396, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146397, "dur": 27, "ph": "X", "name": "ReadAsync 429", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146428, "dur": 32, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146463, "dur": 28, "ph": "X", "name": "ReadAsync 508", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146495, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146497, "dur": 30, "ph": "X", "name": "ReadAsync 326", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146529, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146530, "dur": 26, "ph": "X", "name": "ReadAsync 550", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146558, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146560, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146586, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146606, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146634, "dur": 24, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146662, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146689, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146722, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146747, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146749, "dur": 26, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146777, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146778, "dur": 23, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146803, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146805, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146836, "dur": 22, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146862, "dur": 26, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146891, "dur": 24, "ph": "X", "name": "ReadAsync 269", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146919, "dur": 26, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146948, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146950, "dur": 28, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146983, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469146985, "dur": 28, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147017, "dur": 18, "ph": "X", "name": "ReadAsync 602", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147037, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147059, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147061, "dur": 23, "ph": "X", "name": "ReadAsync 101", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147088, "dur": 32, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147123, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147153, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147156, "dur": 27, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147185, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147186, "dur": 22, "ph": "X", "name": "ReadAsync 410", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147211, "dur": 26, "ph": "X", "name": "ReadAsync 181", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147242, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147269, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147271, "dur": 23, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147298, "dur": 27, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147329, "dur": 23, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147354, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147356, "dur": 23, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147382, "dur": 29, "ph": "X", "name": "ReadAsync 172", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147413, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147415, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147434, "dur": 19, "ph": "X", "name": "ReadAsync 187", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147457, "dur": 25, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147486, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147511, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147513, "dur": 33, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147549, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147568, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147596, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147621, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147623, "dur": 78, "ph": "X", "name": "ReadAsync 498", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147703, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147705, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147724, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147743, "dur": 20, "ph": "X", "name": "ReadAsync 166", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147767, "dur": 25, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147795, "dur": 28, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147826, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147828, "dur": 25, "ph": "X", "name": "ReadAsync 458", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147857, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147885, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147886, "dur": 25, "ph": "X", "name": "ReadAsync 461", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147916, "dur": 31, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147948, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147950, "dur": 23, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469147977, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148003, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148028, "dur": 31, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148061, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148063, "dur": 25, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148091, "dur": 24, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148120, "dur": 15, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148137, "dur": 24, "ph": "X", "name": "ReadAsync 222", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148164, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148165, "dur": 31, "ph": "X", "name": "ReadAsync 167", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148199, "dur": 23, "ph": "X", "name": "ReadAsync 550", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148224, "dur": 25, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148252, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148275, "dur": 21, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148299, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148322, "dur": 20, "ph": "X", "name": "ReadAsync 177", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148345, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148366, "dur": 30, "ph": "X", "name": "ReadAsync 175", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148399, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148401, "dur": 31, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148435, "dur": 37, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148474, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148476, "dur": 30, "ph": "X", "name": "ReadAsync 629", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148509, "dur": 30, "ph": "X", "name": "ReadAsync 543", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148543, "dur": 23, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148568, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148593, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148594, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148620, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148647, "dur": 20, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148670, "dur": 22, "ph": "X", "name": "ReadAsync 204", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148695, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148718, "dur": 24, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148745, "dur": 28, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148775, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148777, "dur": 23, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148803, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148826, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148854, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148877, "dur": 24, "ph": "X", "name": "ReadAsync 200", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148903, "dur": 21, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148927, "dur": 20, "ph": "X", "name": "ReadAsync 238", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148950, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469148975, "dur": 25, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149003, "dur": 27, "ph": "X", "name": "ReadAsync 502", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149032, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149057, "dur": 21, "ph": "X", "name": "ReadAsync 195", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149081, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149107, "dur": 27, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149137, "dur": 21, "ph": "X", "name": "ReadAsync 513", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149160, "dur": 21, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149184, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149209, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149233, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149257, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149281, "dur": 23, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149307, "dur": 79, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149389, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149392, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149418, "dur": 7, "ph": "X", "name": "ProcessMessages 1094", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149427, "dur": 36, "ph": "X", "name": "ReadAsync 1094", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149466, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149468, "dur": 45, "ph": "X", "name": "ReadAsync 232", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149517, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149519, "dur": 24, "ph": "X", "name": "ReadAsync 655", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149545, "dur": 24, "ph": "X", "name": "ReadAsync 570", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149573, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149575, "dur": 30, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149607, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149610, "dur": 29, "ph": "X", "name": "ReadAsync 452", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149641, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149643, "dur": 65, "ph": "X", "name": "ReadAsync 172", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149712, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149714, "dur": 33, "ph": "X", "name": "ReadAsync 539", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149750, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149752, "dur": 25, "ph": "X", "name": "ReadAsync 701", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149791, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149794, "dur": 36, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149832, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149834, "dur": 27, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149863, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149865, "dur": 26, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149894, "dur": 26, "ph": "X", "name": "ReadAsync 466", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149923, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469149925, "dur": 83, "ph": "X", "name": "ReadAsync 212", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150010, "dur": 1, "ph": "X", "name": "ProcessMessages 966", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150012, "dur": 33, "ph": "X", "name": "ReadAsync 966", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150048, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150050, "dur": 34, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150087, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150089, "dur": 33, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150124, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150127, "dur": 34, "ph": "X", "name": "ReadAsync 489", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150165, "dur": 35, "ph": "X", "name": "ReadAsync 222", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150202, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150204, "dur": 26, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150233, "dur": 23, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150259, "dur": 27, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150289, "dur": 122, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150415, "dur": 37, "ph": "X", "name": "ReadAsync 611", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150455, "dur": 2, "ph": "X", "name": "ProcessMessages 1445", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150459, "dur": 31, "ph": "X", "name": "ReadAsync 1445", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150493, "dur": 30, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150527, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150529, "dur": 31, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150562, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150564, "dur": 30, "ph": "X", "name": "ReadAsync 579", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150597, "dur": 1, "ph": "X", "name": "ProcessMessages 102", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150598, "dur": 124, "ph": "X", "name": "ReadAsync 102", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150725, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150727, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150764, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150767, "dur": 29, "ph": "X", "name": "ReadAsync 564", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150798, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150801, "dur": 27, "ph": "X", "name": "ReadAsync 652", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150830, "dur": 58, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150892, "dur": 27, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150922, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150924, "dur": 21, "ph": "X", "name": "ReadAsync 688", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150947, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150949, "dur": 26, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469150978, "dur": 28, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151009, "dur": 24, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151036, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151061, "dur": 26, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151090, "dur": 21, "ph": "X", "name": "ReadAsync 624", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151113, "dur": 26, "ph": "X", "name": "ReadAsync 58", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151141, "dur": 21, "ph": "X", "name": "ReadAsync 652", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151165, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151191, "dur": 21, "ph": "X", "name": "ReadAsync 681", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151215, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151242, "dur": 23, "ph": "X", "name": "ReadAsync 638", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151267, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151268, "dur": 23, "ph": "X", "name": "ReadAsync 421", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151294, "dur": 22, "ph": "X", "name": "ReadAsync 662", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151319, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151342, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151367, "dur": 21, "ph": "X", "name": "ReadAsync 566", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151392, "dur": 29, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151424, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151426, "dur": 38, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151466, "dur": 1, "ph": "X", "name": "ProcessMessages 960", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151468, "dur": 28, "ph": "X", "name": "ReadAsync 960", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151498, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151499, "dur": 23, "ph": "X", "name": "ReadAsync 689", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151526, "dur": 25, "ph": "X", "name": "ReadAsync 454", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151553, "dur": 21, "ph": "X", "name": "ReadAsync 633", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151577, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151604, "dur": 23, "ph": "X", "name": "ReadAsync 513", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151629, "dur": 23, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151655, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151682, "dur": 21, "ph": "X", "name": "ReadAsync 592", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151706, "dur": 26, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151734, "dur": 20, "ph": "X", "name": "ReadAsync 631", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151757, "dur": 35, "ph": "X", "name": "ReadAsync 257", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151796, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151831, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151833, "dur": 31, "ph": "X", "name": "ReadAsync 508", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151865, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151867, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151889, "dur": 23, "ph": "X", "name": "ReadAsync 210", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151914, "dur": 18, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151934, "dur": 2, "ph": "X", "name": "ProcessMessages 43", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151937, "dur": 20, "ph": "X", "name": "ReadAsync 43", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151960, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469151983, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152007, "dur": 22, "ph": "X", "name": "ReadAsync 232", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152032, "dur": 23, "ph": "X", "name": "ReadAsync 565", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152058, "dur": 21, "ph": "X", "name": "ReadAsync 391", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152081, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152104, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152126, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152151, "dur": 49, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152203, "dur": 28, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152233, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152236, "dur": 18, "ph": "X", "name": "ReadAsync 733", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152256, "dur": 31, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152291, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152318, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152320, "dur": 26, "ph": "X", "name": "ReadAsync 683", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152349, "dur": 21, "ph": "X", "name": "ReadAsync 685", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152372, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152398, "dur": 22, "ph": "X", "name": "ReadAsync 641", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152423, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152449, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152474, "dur": 19, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152496, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152519, "dur": 20, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152542, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152564, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152586, "dur": 23, "ph": "X", "name": "ReadAsync 249", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152612, "dur": 23, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152637, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152639, "dur": 35, "ph": "X", "name": "ReadAsync 365", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152677, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152679, "dur": 30, "ph": "X", "name": "ReadAsync 436", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152711, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152713, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152741, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152743, "dur": 25, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152770, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152772, "dur": 38, "ph": "X", "name": "ReadAsync 618", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152814, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152841, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152865, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152890, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152910, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152933, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152955, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469152980, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153003, "dur": 20, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153026, "dur": 22, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153051, "dur": 27, "ph": "X", "name": "ReadAsync 469", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153079, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153081, "dur": 20, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153104, "dur": 27, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153134, "dur": 23, "ph": "X", "name": "ReadAsync 630", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153159, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153161, "dur": 25, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153188, "dur": 19, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153211, "dur": 20, "ph": "X", "name": "ReadAsync 43", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153233, "dur": 20, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153256, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153277, "dur": 18, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153298, "dur": 19, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153320, "dur": 19, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153341, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153343, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153374, "dur": 20, "ph": "X", "name": "ReadAsync 25", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153397, "dur": 2, "ph": "X", "name": "ProcessMessages 866", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153400, "dur": 20, "ph": "X", "name": "ReadAsync 866", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153423, "dur": 20, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153447, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153471, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153493, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153516, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153548, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153573, "dur": 38, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153612, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153614, "dur": 20, "ph": "X", "name": "ReadAsync 718", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153637, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153661, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153684, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153707, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153730, "dur": 20, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153753, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153776, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153799, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153822, "dur": 20, "ph": "X", "name": "ReadAsync 210", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153845, "dur": 20, "ph": "X", "name": "ReadAsync 238", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153868, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153891, "dur": 20, "ph": "X", "name": "ReadAsync 135", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153913, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153937, "dur": 21, "ph": "X", "name": "ReadAsync 238", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153960, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469153984, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154003, "dur": 21, "ph": "X", "name": "ReadAsync 214", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154027, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154051, "dur": 32, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154086, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154109, "dur": 21, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154133, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154156, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154180, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154201, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154225, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154249, "dur": 20, "ph": "X", "name": "ReadAsync 266", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154272, "dur": 18, "ph": "X", "name": "ReadAsync 261", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154293, "dur": 19, "ph": "X", "name": "ReadAsync 222", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154315, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154339, "dur": 23, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154365, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154386, "dur": 22, "ph": "X", "name": "ReadAsync 216", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469154412, "dur": 32910, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187331, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187335, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187366, "dur": 394, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187765, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187768, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187787, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187788, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187825, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187827, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187858, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187860, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187876, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187878, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187924, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469187956, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188013, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188018, "dur": 516, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188537, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188539, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188564, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188612, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188645, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188675, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188677, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188715, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188717, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188756, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188799, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188829, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188869, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188870, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188901, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469188904, "dur": 270, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189179, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189215, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189217, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189257, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189258, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189293, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189296, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189332, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189372, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189401, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189403, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189474, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189510, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189512, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189541, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189543, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189591, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189618, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189620, "dur": 234, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189860, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189907, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189909, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189940, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189942, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189985, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469189988, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190033, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190086, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190123, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190125, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190177, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190208, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190233, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190237, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190276, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190356, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190395, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190398, "dur": 255, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190658, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190692, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190728, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190789, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190822, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190824, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190885, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190887, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190915, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190945, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190947, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469190981, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191010, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191039, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191087, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191119, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191324, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191358, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191360, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191425, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191451, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191523, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191525, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191558, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191560, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191598, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191600, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191660, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191703, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191705, "dur": 195, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191907, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191958, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469191960, "dur": 316, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192282, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192346, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192348, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192367, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192405, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192427, "dur": 97, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192528, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192566, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192567, "dur": 339, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192911, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192930, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469192987, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193032, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193034, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193128, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193130, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193164, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193166, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193193, "dur": 284, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193481, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193520, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193522, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193554, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193590, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193608, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193638, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193686, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193721, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193757, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193803, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193806, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193857, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193888, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193917, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469193918, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194201, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194217, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194219, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194276, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194278, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194309, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194311, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194356, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194386, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194388, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194416, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194443, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194445, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194509, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194553, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194592, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194622, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194832, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194865, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194924, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469194958, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195003, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195031, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195033, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195079, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195113, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195115, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195182, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195216, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195251, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195278, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195281, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195328, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195362, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195532, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195569, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195571, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195661, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195697, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195849, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195879, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195881, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195919, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195921, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469195957, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196035, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196065, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196067, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196111, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196148, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196185, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196366, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196392, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196478, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196502, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196553, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196612, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196614, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196629, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196639, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196701, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196744, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196746, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196790, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196791, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196845, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196883, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196957, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469196973, "dur": 260, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197238, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197263, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197265, "dur": 57, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197327, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197393, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197395, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197458, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197460, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197524, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197552, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197706, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197787, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469197789, "dur": 593, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198385, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198387, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198412, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198415, "dur": 83, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198500, "dur": 15, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198517, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198535, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198696, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198736, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198738, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198799, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198803, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198915, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469198918, "dur": 90, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199011, "dur": 7, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199021, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199091, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199155, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199157, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199192, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199194, "dur": 161, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199359, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199375, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199377, "dur": 541, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199924, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199962, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469199964, "dur": 4639, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469204612, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469204617, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469204659, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469204680, "dur": 799, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469205485, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469205522, "dur": 6, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469205529, "dur": 267, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469205801, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469205819, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469205822, "dur": 1476, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469207302, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469207337, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469207341, "dur": 1062, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469208408, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469208440, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469208443, "dur": 6321, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469214773, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469214777, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469214811, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469214815, "dur": 69705, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469284529, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469284535, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469284576, "dur": 16, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469284594, "dur": 128, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469284727, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469284770, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469284774, "dur": 6088, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469290875, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469290882, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469290937, "dur": 23, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469290962, "dur": 333, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469291299, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469291301, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469291318, "dur": 5, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469291324, "dur": 233, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469291562, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469291604, "dur": 15, "ph": "X", "name": "ProcessMessages 126", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469291621, "dur": 498, "ph": "X", "name": "ReadAsync 126", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469292124, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469292147, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469292149, "dur": 9040, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469301303, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469301310, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469301349, "dur": 32, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469301383, "dur": 8631, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469310027, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469310035, "dur": 338, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469310379, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469310403, "dur": 32, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469310437, "dur": 6, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469310444, "dur": 6971, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469317428, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469317434, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469317485, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469317503, "dur": 29, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469317534, "dur": 3, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469317538, "dur": 169, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469317713, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469317729, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469317734, "dur": 3470, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469321216, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469321222, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469321285, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469321306, "dur": 12550, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469333868, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469333873, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469333919, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469333938, "dur": 110978, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469444926, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469444930, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469444972, "dur": 19, "ph": "X", "name": "ProcessMessages 444", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469444993, "dur": 54651, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469499654, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469499659, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469499696, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469499699, "dur": 1405, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469501110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469501113, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469501188, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469501204, "dur": 58906, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469560120, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469560124, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469560155, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469560170, "dur": 342513, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469902692, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469902696, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469902730, "dur": 26, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352469902756, "dur": 110192, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470012957, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470012961, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470012984, "dur": 15724, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470028718, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470028722, "dur": 7090, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470035821, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470035826, "dur": 5586, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470041422, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470041427, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470041456, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470041458, "dur": 1193, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470042655, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470042657, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470042770, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470042772, "dur": 15083, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470057866, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470057872, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470057910, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470057912, "dur": 5789, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470063711, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470063715, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470063846, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470063852, "dur": 427, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470064284, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470064286, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470064309, "dur": 7562, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470071883, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470071889, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470071938, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470071941, "dur": 31676, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470103627, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470103632, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470103661, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470103662, "dur": 10616, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470114290, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470114295, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470114354, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470114358, "dur": 1680, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470116042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470116044, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470116067, "dur": 4201, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470120274, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470120276, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470120342, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470120344, "dur": 7452, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470127806, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470127811, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470127850, "dur": 11667, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470139526, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470139531, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470139569, "dur": 3636, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470143213, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470143218, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470143263, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470143266, "dur": 24107, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470167384, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470167390, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470167436, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470167439, "dur": 42230, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470209679, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470209684, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470209738, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470209741, "dur": 27639, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470237389, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470237393, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470237458, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470237462, "dur": 12867, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470250341, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470250346, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470250376, "dur": 322, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470250702, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470250752, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470250755, "dur": 10049, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470260813, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470260817, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470260852, "dur": 3195, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470264053, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470264056, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470264083, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470264084, "dur": 29838, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470293931, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470293936, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470293972, "dur": 29480, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470323461, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470323465, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470323497, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470323499, "dur": 13152, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470336659, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470336664, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470336692, "dur": 49065, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470385766, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470385771, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470385804, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470385806, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470385867, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470385869, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470385903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470385905, "dur": 58549, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470444466, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470444471, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470444547, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470444550, "dur": 854, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470445409, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470445434, "dur": 26438, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470471881, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470471885, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470471923, "dur": 72534, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470544467, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470544471, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470544499, "dur": 453, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470544961, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470544964, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470544986, "dur": 15171, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470560165, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470560170, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470560202, "dur": 37131, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470597342, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470597347, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470597381, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470597388, "dur": 31020, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470628416, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470628421, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470628467, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470628469, "dur": 34803, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470663283, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470663287, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470663317, "dur": 27818, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470691145, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470691150, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470691186, "dur": 29050, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470720245, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470720250, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470720276, "dur": 7826, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470728111, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470728115, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470728144, "dur": 23225, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470751380, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470751385, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470751422, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470751423, "dur": 2371, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470753801, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470753805, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470753832, "dur": 28934, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470782775, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470782780, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470782815, "dur": 1316, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470784135, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470784138, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470784175, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470784178, "dur": 8413, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470792600, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470792605, "dur": 242, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470792850, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470792854, "dur": 972, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470793831, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470793834, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470793874, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470793877, "dur": 4157, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470798044, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470798048, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470798079, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470798081, "dur": 17832, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470815922, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470815926, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470815954, "dur": 35628, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470851591, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470851596, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470851627, "dur": 2729, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470854364, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470854368, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470854399, "dur": 1805, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470856209, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470856213, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470856307, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470856309, "dur": 2603, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470858918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470858921, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470858945, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470858947, "dur": 5792, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470864747, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470864751, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470864801, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470864804, "dur": 8299, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470873113, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470873118, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470873154, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470873157, "dur": 309, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470873469, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470873471, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470873492, "dur": 502, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470873998, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470874000, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470874034, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470874036, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470874178, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470874199, "dur": 336, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470874539, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470874566, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470874568, "dur": 5720, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880296, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880300, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880360, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880362, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880516, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880551, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880553, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880662, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880693, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880847, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880896, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470880897, "dur": 688, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470881590, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470881612, "dur": 184, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470881802, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470881841, "dur": 597, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470882443, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470882472, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470882480, "dur": 791, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470883275, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470883277, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470883311, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470883483, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470883511, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470883513, "dur": 481, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470883998, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470884028, "dur": 402, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470884435, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470884465, "dur": 6718, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470891193, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470891197, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470891228, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470891229, "dur": 24146, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915395, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915402, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915431, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915432, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915564, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915578, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915688, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915721, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915723, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915870, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915901, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470915903, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916015, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916040, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916041, "dur": 176, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916222, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916248, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916250, "dur": 433, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916686, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916689, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916717, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470916718, "dur": 465, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470917188, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470917210, "dur": 3123, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470920337, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470920339, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470920370, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470920372, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470920517, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470920519, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470920548, "dur": 5035, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470925590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470925592, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470925634, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470925636, "dur": 501, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470926141, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470926177, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470926179, "dur": 37257, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470963446, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470963451, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470963489, "dur": 16551, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470980049, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470980054, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470980089, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352470980090, "dur": 30036, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471010135, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471010140, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471010170, "dur": 8585, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471018786, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471018793, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471018833, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471018836, "dur": 229, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471019070, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471019104, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471019108, "dur": 17143, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471036260, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471036264, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471036292, "dur": 24718, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471061019, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471061027, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471061054, "dur": 49298, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471110361, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471110366, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471110390, "dur": 3043, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471113440, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471113444, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471113480, "dur": 66670, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471180159, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471180164, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471180200, "dur": 5243, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471185452, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471185457, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471185489, "dur": 333, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471185828, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471185866, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471185870, "dur": 9493, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471195372, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471195376, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471195423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471195425, "dur": 269, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471195699, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471195741, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471195964, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471195991, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471196149, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471196178, "dur": 408, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471196590, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471196615, "dur": 4420, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471201043, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471201047, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471201073, "dur": 6810, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471207893, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471207898, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471207934, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471207936, "dur": 10688, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471218632, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471218637, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471218683, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471218686, "dur": 17553, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471236260, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471236264, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471236287, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471236289, "dur": 21833, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471258130, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471258134, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471258158, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471258160, "dur": 3155, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471261322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471261325, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471261355, "dur": 17908, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471279285, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471279290, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471279349, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471279351, "dur": 49175, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471328535, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471328539, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471328572, "dur": 389, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471328966, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471329007, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471329009, "dur": 318, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471329332, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471329363, "dur": 404, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471329772, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471329806, "dur": 303, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471330114, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471330147, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471330149, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471330183, "dur": 665, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471330854, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471330873, "dur": 992, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471331867, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471331869, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471331903, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471331905, "dur": 29214, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471361128, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471361135, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471361167, "dur": 10732, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471371908, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471371912, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471371941, "dur": 8693, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471380641, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471380646, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471380699, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471380702, "dur": 17824, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471398533, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471398537, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471398557, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471398560, "dur": 3417, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471401984, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471401988, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471402015, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471402019, "dur": 8146, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471410173, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471410178, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471410231, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471410233, "dur": 890, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471411129, "dur": 1230, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471412364, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471412367, "dur": 1988, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414359, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414362, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414381, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414557, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414559, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414576, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414640, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414642, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414675, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414677, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414781, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414900, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414978, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471414980, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471415091, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471415113, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471415256, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471415319, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471415321, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471415354, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471415356, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471415456, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471415498, "dur": 3902, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471419407, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471419411, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471419505, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471419508, "dur": 18285, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471437802, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471437807, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471437861, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471437866, "dur": 1665, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471439535, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471439652, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471439669, "dur": 17235, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471456913, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471456920, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471456948, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471456949, "dur": 1767, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471458721, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471458724, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471458760, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471458762, "dur": 12140, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471470910, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471470914, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471470947, "dur": 295, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471248, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471280, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471282, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471400, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471439, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471537, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471573, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471575, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471708, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471741, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471471744, "dur": 777, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471472524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471472526, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471472545, "dur": 487, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471473037, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471473095, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471473098, "dur": 1249, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471474350, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471474352, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471474383, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471474385, "dur": 14851, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471489245, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471489250, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471489280, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471489282, "dur": 470, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471489756, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471489759, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471489800, "dur": 54998, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471544807, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471544812, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471544874, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471544878, "dur": 901, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471545784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471545786, "dur": 475, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471546264, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471546267, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471546314, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471546317, "dur": 806, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547134, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547178, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547228, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547230, "dur": 382, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547618, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547655, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547657, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547771, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471547802, "dur": 323, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548130, "dur": 399, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548533, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548535, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548571, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548573, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548604, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548606, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548788, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548790, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471548811, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471549013, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471549043, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471549045, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471549399, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471549432, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471549434, "dur": 653, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471550095, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471550121, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471550295, "dur": 14523, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471564828, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471564833, "dur": 15621, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471580463, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471580467, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471580500, "dur": 3134, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471583642, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471583645, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471583671, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471583793, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471583795, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471583833, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471583835, "dur": 164, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471584004, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471584039, "dur": 4432, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471588478, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471588482, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471588510, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471588512, "dur": 16019, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471604540, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471604544, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471604598, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471604600, "dur": 357, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471604962, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471604984, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605099, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605103, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605134, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605265, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605288, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605431, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605492, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605697, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605722, "dur": 232, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471605958, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471606003, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471606005, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471606162, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471606193, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471606316, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471606342, "dur": 8793, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471615143, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471615148, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471615180, "dur": 1276, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471616460, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471616462, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471616500, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471616504, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471616655, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471616684, "dur": 314, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471617004, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471617024, "dur": 4775, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471621807, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471621811, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471621838, "dur": 18575, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471640423, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471640428, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471640459, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471640461, "dur": 15387, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471655858, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471655862, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471655895, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471655897, "dur": 5303, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471661209, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471661213, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471661241, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471661243, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471661436, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471661438, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471661464, "dur": 18767, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680241, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680246, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680328, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680330, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680372, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680407, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680409, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680523, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680554, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680556, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680677, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680711, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680713, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680818, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680851, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680853, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680954, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680958, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471680989, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681128, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681156, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681289, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681318, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681320, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681496, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681525, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681527, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681664, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681667, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681697, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681699, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681881, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681909, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471681910, "dur": 384, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471682298, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471682353, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471682358, "dur": 1362, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471683725, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471683773, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471683775, "dur": 650, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471684430, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471684487, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471684490, "dur": 462, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471684958, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471684995, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471685162, "dur": 365, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471685532, "dur": 761, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471686296, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471686298, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471686317, "dur": 840, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471687160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471687162, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471687190, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471687191, "dur": 13550, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471700750, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471700755, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471700860, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471700862, "dur": 30266, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471731137, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471731141, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471731172, "dur": 4431, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471735611, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471735615, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471735651, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471735653, "dur": 7825, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471743486, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471743491, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471743541, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471743544, "dur": 369, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471743918, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471743975, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471743977, "dur": 292, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471744274, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471744309, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471744396, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471744412, "dur": 545, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471744969, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471744975, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471745069, "dur": 5462, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471750540, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471750544, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471750589, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471750591, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471750684, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471750717, "dur": 29624, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471780350, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471780354, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471780384, "dur": 429, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471780816, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471780818, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471780850, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471780852, "dur": 15212, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471796073, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471796077, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471796113, "dur": 66838, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471862959, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471862964, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471862999, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471863001, "dur": 372, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471863376, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471863387, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471863720, "dur": 810, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471864536, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471864587, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471864589, "dur": 798, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471865392, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471865412, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471865413, "dur": 180, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471865598, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471865653, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471865777, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471865914, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471865916, "dur": 558, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471866479, "dur": 674, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471867156, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471867158, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471867233, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471867261, "dur": 2760, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471870028, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471870031, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471870063, "dur": 1936, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471872004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471872007, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471872070, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471872072, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471872129, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471872131, "dur": 197, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471872372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471872376, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471872400, "dur": 2213, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471874628, "dur": 14, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471874644, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471874718, "dur": 9, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471874729, "dur": 410, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471875145, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471875228, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471875231, "dur": 612, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471875848, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471875866, "dur": 352, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471876223, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471876255, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471876572, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471876592, "dur": 298, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471876896, "dur": 265, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471877164, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471877166, "dur": 459, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471877631, "dur": 2624, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471880385, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471880391, "dur": 2995, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471883394, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471883398, "dur": 15585, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471898993, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471898998, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471899072, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471899076, "dur": 504, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471899587, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471899606, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471899660, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471899681, "dur": 2559, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471902245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471902248, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471902286, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471902288, "dur": 34476, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471936773, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471936778, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471936858, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471936862, "dur": 60129, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471996999, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471997003, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471997031, "dur": 8, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471997042, "dur": 878, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471997926, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471997966, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352471997968, "dur": 107440, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472105418, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472105423, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472105474, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472105477, "dur": 1092, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472106572, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472106574, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472106612, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472106614, "dur": 308, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472106926, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472106928, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472106978, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472106980, "dur": 515, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472107501, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472107541, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754352472107543, "dur": 20839, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 54635968, "ts": 1754352472130658, "dur": 6264, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 68719476736, "ts": 1754352469088803, "dur": 3039821, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754352469088928, "dur": 13491, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754352472128630, "dur": 9, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754352472128641, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 54635968, "ts": 1754352472136926, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754352468865032, "dur": 2753, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754352468867791, "dur": 219549, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754352469087342, "dur": 1433, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 93864, "tid": 54635968, "ts": 1754352472136937, "dur": 6, "ph": "X", "name": "", "args": {} },
{ "pid": 239640, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 239640, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 239640, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 239640, "tid": 1, "ts": 1754352469443175, "dur": 422552, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 239640, "tid": 1, "ts": 1754352469445489, "dur": 70867, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469455195, "dur": 59073, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469542291, "dur": 15337, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469558899, "dur": 79040, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469638001, "dur": 19559, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469657581, "dur": 198352, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469862802, "dur": 2691, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469865729, "dur": 456, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469873886, "dur": 2676, "ph": "X", "name": "", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469873112, "dur": 3844, "ph": "X", "name": "Write chrome-trace events", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754352469137356, "dur":3733, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352469141102, "dur":530, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352469141673, "dur":50, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1754352469141724, "dur":603, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352469142705, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":0, "ts":1754352469144071, "dur":91, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":0, "ts":1754352469144245, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Collections.dll" }}
,{ "pid":12345, "tid":0, "ts":1754352469144319, "dur":142, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Collections.LowLevel.ILSupport.dll" }}
,{ "pid":12345, "tid":0, "ts":1754352469144892, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1754352469145770, "dur":159, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1754352469146322, "dur":121, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1754352469148371, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Net.Http-FeaturesChecked.txt_iku7.info" }}
,{ "pid":12345, "tid":0, "ts":1754352469150111, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754352469150692, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754352469151309, "dur":148, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ClusterInputModule-FeaturesChecked.txt_frb1.info" }}
,{ "pid":12345, "tid":0, "ts":1754352469152475, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754352469142362, "dur":12760, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352469155131, "dur":2952571, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352472107703, "dur":273, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352472108222, "dur":3606, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754352469142607, "dur":12535, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352469161843, "dur":484, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":1, "ts":1754352469162327, "dur":1995, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1754352469164322, "dur":17762, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1754352469182085, "dur":340, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":1, "ts":1754352469182425, "dur":3940, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":1, "ts":1754352469186366, "dur":271, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":1, "ts":1754352469186637, "dur":495, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1754352469187132, "dur":429, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":1, "ts":1754352469187561, "dur":131, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":1, "ts":1754352469187692, "dur":81, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1754352469155151, "dur":32642, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352469187806, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754352469187881, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352469188303, "dur":786, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469189094, "dur":202, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469189298, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469189471, "dur":160, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469189636, "dur":143, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469189784, "dur":274, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469190059, "dur":445, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469190506, "dur":611, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469191119, "dur":206, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469191327, "dur":865, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469192194, "dur":239, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469192435, "dur":194, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469192636, "dur":819, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469193457, "dur":216, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469193675, "dur":254, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469193935, "dur":185, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469194127, "dur":342, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469194475, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469194669, "dur":197, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469194871, "dur":413, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469195286, "dur":922, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469196209, "dur":209, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469196420, "dur":184, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469196606, "dur":192, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469196800, "dur":327, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469197129, "dur":208, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469197340, "dur":2500, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469199847, "dur":249, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469200099, "dur":232, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469200333, "dur":577, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469200912, "dur":296, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469201215, "dur":289, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469201506, "dur":750, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469202258, "dur":575, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469202835, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469203093, "dur":375, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469204129, "dur":146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":1, "ts":1754352469204277, "dur":171, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754352469204450, "dur":173, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754352469204625, "dur":292, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":1, "ts":1754352469188025, "dur":17302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754352469206350, "dur":163, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352469256272, "dur":647117, "ph":"X", "name": "UnityLinker",  "args": { "detail":"C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754352469970203, "dur":684458, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352470654877, "dur":45733, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352470700840, "dur":52296, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352470753278, "dur":67023, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352470820442, "dur":432852, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Mono.Security.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352471253300, "dur":46196, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\netstandard.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352471300308, "dur":249697, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352471550016, "dur":248166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Drawing.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352471798195, "dur":199490, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352471997693, "dur":107729, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Xml.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352469968652, "dur":2136960, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1754352472105613, "dur":498, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352472107384, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352472107500, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469142633, "dur":12518, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469155559, "dur":961, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\WindowsBase.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469156521, "dur":952, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":2, "ts":1754352469157474, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":2, "ts":1754352469158108, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":2, "ts":1754352469158794, "dur":1030, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469159824, "dur":769, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":2, "ts":1754352469160593, "dur":767, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469161360, "dur":764, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469162124, "dur":1011, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":2, "ts":1754352469163135, "dur":941, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469164076, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469164736, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469165361, "dur":820, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469166181, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469166843, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469167471, "dur":941, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469168412, "dur":735, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469155164, "dur":13983, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469169148, "dur":788, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":2, "ts":1754352469169937, "dur":926, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":2, "ts":1754352469170863, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.exe" }}
,{ "pid":12345, "tid":2, "ts":1754352469174331, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Tools.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469169148, "dur":6444, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469180825, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469175592, "dur":6260, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469181852, "dur":233, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469182086, "dur":5711, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469187825, "dur":814, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469188652, "dur":874, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469189534, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469190237, "dur":746, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469190989, "dur":674, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469191668, "dur":686, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469192362, "dur":845, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469193215, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469193789, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469194487, "dur":742, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469195237, "dur":692, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469195940, "dur":835, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469196781, "dur":692, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469197477, "dur":263, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469197740, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469197878, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":2, "ts":1754352469198030, "dur":352, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469198389, "dur":660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":2, "ts":1754352469199050, "dur":390, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469200958, "dur":4201, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":2, "ts":1754352469205657, "dur":112579, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":2, "ts":1754352469318366, "dur":650417, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352469969706, "dur":72163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestAssetBundleModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352469968784, "dur":73087, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352470041872, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352470042260, "dur":101483, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.CrashReportingModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352470042102, "dur":101643, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.CrashReportingModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352470143746, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352470144046, "dur":106716, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ContentLoadModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352470143903, "dur":106934, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ContentLoadModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754352470250838, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352470251483, "dur":72448, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.WindModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352470251368, "dur":72599, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.WindModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754352470323972, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352470324124, "dur":56, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.WindModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754352470324577, "dur":469739, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352470324461, "dur":469895, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754352470794357, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352470794992, "dur":78922, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.LocalizationModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352470794872, "dur":79043, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.LocalizationModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352470873916, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352470874492, "dur":343891, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352470874187, "dur":344238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.TextMeshPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754352471218426, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471219148, "dur":385864, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352471219029, "dur":386024, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754352471605054, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471605212, "dur":113, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754352471605567, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471605704, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471605871, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471606011, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471606151, "dur":103, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352471606258, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471606449, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471606742, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471606889, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471607666, "dur":390556, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352471607058, "dur":391165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352471998224, "dur":404, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352471998652, "dur":109054, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469142663, "dur":12501, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469155171, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469155928, "dur":856, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469156785, "dur":802, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469157587, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469158198, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469158921, "dur":1053, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469159974, "dur":787, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469160762, "dur":796, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469161558, "dur":892, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469162450, "dur":800, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469163250, "dur":955, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469164206, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":3, "ts":1754352469164866, "dur":785, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469165651, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469166298, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469166996, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469167633, "dur":1077, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469168711, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469169400, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469170181, "dur":1114, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469155171, "dur":16124, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469172181, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\PEAPI.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469172754, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Novell.Directory.Ldap.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469173782, "dur":832, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.WebBrowser.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469174614, "dur":641, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Tasklets.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469175256, "dur":856, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Simd.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469176595, "dur":615, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Security.Win32.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469180054, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Http.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469171295, "dur":9929, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469181384, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.GridModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469183424, "dur":568, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469181224, "dur":4885, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469186109, "dur":1690, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469187825, "dur":773, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469188608, "dur":803, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469189417, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469189599, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469190319, "dur":779, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469191102, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469191815, "dur":807, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469192647, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469193220, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469193848, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469194583, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469195303, "dur":667, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469195978, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469196832, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469197486, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":3, "ts":1754352469197666, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469197782, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":3, "ts":1754352469197963, "dur":260, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469198227, "dur":403, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":3, "ts":1754352469198631, "dur":1177, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469200907, "dur":100305, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets" }}
,{ "pid":12345, "tid":3, "ts":1754352469301350, "dur":667300, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352469969392, "dur":59795, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352469968651, "dur":60538, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470029195, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470029384, "dur":167, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470030942, "dur":89489, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470030705, "dur":89778, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352470120484, "dur":482, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470121385, "dur":129789, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.InputLegacyModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470121279, "dur":129977, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352470251256, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470251416, "dur":59, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352470251846, "dur":192901, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.GridModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470251740, "dur":193052, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352470444793, "dur":332, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470445126, "dur":136, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352470445678, "dur":183237, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470445550, "dur":183366, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470628918, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470629325, "dur":99219, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470629097, "dur":99449, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470728548, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470729019, "dur":151813, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470728839, "dur":151994, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470880834, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470880975, "dur":107, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470881087, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470881242, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470881393, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470881533, "dur":100, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.MarshallingModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470881637, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470882319, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470882534, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470883166, "dur":37619, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470883164, "dur":37623, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470920788, "dur":262, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470921084, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352470921452, "dur":281702, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Numerics.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352470921245, "dur":282099, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Numerics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352471203345, "dur":5206, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471209023, "dur":70695, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\netstandard.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471208900, "dur":70861, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/netstandard-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352471279763, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471279929, "dur":155, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/netstandard-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352471280488, "dur":48042, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471280354, "dur":48178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471356976, "dur":4609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471361587, "dur":234, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471362053, "dur":180030, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.HotReloadModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471361825, "dur":180463, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.HotReloadModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352471542289, "dur":3193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471546198, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471546314, "dur":181, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471546496, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352471546625, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471548282, "dur":202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471548794, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471549263, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471549937, "dur":38999, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityTestProtocolModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471549802, "dur":39177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityTestProtocolModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352471588980, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471589592, "dur":66711, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestTextureModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471589475, "dur":66871, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestTextureModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352471656347, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471657504, "dur":138903, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ClusterRendererModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471657066, "dur":139382, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterRendererModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352471796449, "dur":299, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471796749, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterRendererModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754352471797765, "dur":104910, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471797535, "dur":105142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352471902677, "dur":271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352471902967, "dur":204738, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469142693, "dur":12479, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469155179, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469155872, "dur":834, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469156706, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469157288, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469157949, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469158582, "dur":1069, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469159651, "dur":851, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469160502, "dur":797, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469161299, "dur":777, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469162076, "dur":1056, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469163133, "dur":907, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469164040, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469164741, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469165396, "dur":770, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469166166, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469166872, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469167567, "dur":965, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469168533, "dur":693, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469169226, "dur":777, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469170003, "dur":972, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469155179, "dur":15796, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469170976, "dur":2876, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469173852, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.CJK.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469175119, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469178772, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469173852, "dur":6197, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469180375, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469180962, "dur":503, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469180049, "dur":5258, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469185307, "dur":2571, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469187880, "dur":800, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469188685, "dur":923, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469189614, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469190329, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469191080, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469191741, "dur":652, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469192399, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469193069, "dur":740, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469193814, "dur":667, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469194490, "dur":675, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469195171, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469195825, "dur":834, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469196665, "dur":707, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469197378, "dur":510, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469197898, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":4, "ts":1754352469198058, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469198258, "dur":313, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":4, "ts":1754352469198572, "dur":818, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469199401, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469200898, "dur":91365, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":4, "ts":1754352469292625, "dur":25453, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":4, "ts":1754352469318196, "dur":650493, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352469969547, "dur":95258, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AMDModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352469968690, "dur":96148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AMDModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352470064839, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470065373, "dur":74668, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470065267, "dur":74776, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470140045, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470140332, "dur":124222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SpriteShapeModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470140206, "dur":124386, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352470264598, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470265138, "dur":109432, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.PropertiesModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470265022, "dur":109593, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352470374617, "dur":11822, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470387036, "dur":85084, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470386795, "dur":85368, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352470472166, "dur":315, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470472866, "dur":87653, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.PerformanceReportingModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470472737, "dur":87785, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.PerformanceReportingModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470560524, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470561407, "dur":192871, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470560853, "dur":193469, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352470754324, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470754826, "dur":97285, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470754714, "dur":97399, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470852115, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470852395, "dur":4362, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470852270, "dur":4489, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470856760, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470856912, "dur":16618, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470856909, "dur":16660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.ShaderLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352470873571, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352470874133, "dur":145063, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352470874021, "dur":145219, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352471019242, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471019772, "dur":217001, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471019649, "dur":217126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471236776, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471237081, "dur":91910, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471236929, "dur":92102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352471329033, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471329548, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471329928, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471330343, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471330741, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471331202, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471331653, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471332169, "dur":107890, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471332054, "dur":108044, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AnimationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352471440099, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471440597, "dur":105989, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471440481, "dur":106106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471546588, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471546767, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471547354, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471548178, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471548681, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471549179, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471549598, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471549981, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471550473, "dur":33817, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ShaderVariantAnalyticsModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471550356, "dur":33974, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ShaderVariantAnalyticsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352471584330, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471584897, "dur":76774, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471584732, "dur":76941, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471661678, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471661877, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471662536, "dur":81385, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestWWWModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471662393, "dur":81574, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestWWWModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352471743968, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471744498, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471744856, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471744988, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471745122, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471745252, "dur":252201, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352471745248, "dur":252248, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Runtime.Serialization-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754352471997498, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352471997913, "dur":109798, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469142717, "dur":12463, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469155189, "dur":838, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469156027, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469156738, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469157366, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469158033, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469158743, "dur":1146, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469159889, "dur":796, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469160685, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469161463, "dur":1150, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469162614, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469163348, "dur":1019, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469164846, "dur":784, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469165630, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469166292, "dur":822, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469167114, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469167803, "dur":888, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469168692, "dur":744, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469169436, "dur":809, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469170245, "dur":836, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469155189, "dur":15892, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469172797, "dur":503, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Services.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469174244, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Razor.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469174840, "dur":964, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Mvc.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469176247, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Http.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469178580, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.DynamicData.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469180026, "dur":635, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Transactions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469171081, "dur":9580, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469180661, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469180661, "dur":5236, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469185898, "dur":1909, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469187828, "dur":718, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469188556, "dur":824, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469189386, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469190020, "dur":782, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469190809, "dur":757, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469191572, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469192243, "dur":1037, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469193285, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469193901, "dur":723, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469194630, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469195331, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469196048, "dur":826, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469196880, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469197497, "dur":222, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":5, "ts":1754352469197719, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469197906, "dur":378, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":5, "ts":1754352469198284, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469198476, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469198623, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469198765, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":5, "ts":1754352469198894, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469200643, "dur":110440, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets" }}
,{ "pid":12345, "tid":5, "ts":1754352469311213, "dur":657453, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352469969429, "dur":88823, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TLSModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352469968668, "dur":89628, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TLSModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352470058297, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352470058845, "dur":202483, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470058724, "dur":202606, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470261336, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352470261625, "dur":458957, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470261497, "dur":459128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352470720627, "dur":259, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352470721353, "dur":61888, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470721223, "dur":62020, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470783245, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352470783676, "dur":947, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470783452, "dur":1212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352470784665, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352470785252, "dur":31190, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470785131, "dur":31313, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470816446, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352470816743, "dur":38146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.GraphicsStateCollectionSerializerModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470816603, "dur":38288, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470854892, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352470855185, "dur":10089, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470855065, "dur":10212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470865278, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352470865581, "dur":114921, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470865432, "dur":115113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352470980547, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352470981051, "dur":417952, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352470980938, "dur":418108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352471399048, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471399545, "dur":73126, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityCurlModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471399430, "dur":73275, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityCurlModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352471472706, "dur":533, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471473488, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471473724, "dur":76952, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471473608, "dur":77069, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471550678, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471550846, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471551440, "dur":89520, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471551311, "dur":89651, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471640963, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471641259, "dur":59808, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471641107, "dur":60003, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352471701112, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471701951, "dur":34136, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ScreenCaptureModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471701810, "dur":34315, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ScreenCaptureModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352471736126, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471737146, "dur":43573, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.JSONSerializeModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471736524, "dur":44237, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352471780763, "dur":258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471781344, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471781505, "dur":88466, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471781502, "dur":88471, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471869982, "dur":738, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471870906, "dur":1016, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471870730, "dur":1193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352471871924, "dur":578, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471872511, "dur":310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471872829, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471873073, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754352471873200, "dur":396, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471873944, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471874323, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471874739, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471875275, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471875668, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471876226, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471876880, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471877457, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471878395, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471878831, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352471880440, "dur":227247, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469142748, "dur":12441, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469155197, "dur":769, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469155966, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469156719, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469157440, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469158094, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469158792, "dur":906, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469159698, "dur":833, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469160531, "dur":817, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469161348, "dur":1107, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469162456, "dur":766, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469163222, "dur":1010, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469164232, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469164832, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469165613, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469166217, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469166902, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469167610, "dur":921, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469168531, "dur":759, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469169291, "dur":852, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469170143, "dur":867, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469155196, "dur":15814, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469171567, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\mscorlib.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469173863, "dur":815, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\SystemWebTestShim.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469174678, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469175308, "dur":864, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469171010, "dur":8466, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469180940, "dur":616, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469179477, "dur":5656, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469185133, "dur":2671, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469187835, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469188490, "dur":791, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469189288, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469189902, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469190582, "dur":790, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469191381, "dur":658, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469192048, "dur":896, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469192950, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469193645, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469194319, "dur":686, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469195011, "dur":733, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469195749, "dur":846, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469196600, "dur":826, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469197437, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469197571, "dur":219, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":6, "ts":1754352469197791, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469197938, "dur":784, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":6, "ts":1754352469198723, "dur":481, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469215472, "dur":76852, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":6, "ts":1754352469292741, "dur":41466, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":6, "ts":1754352469334318, "dur":634342, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352469969714, "dur":146808, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352469968663, "dur":147902, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/BakeryRuntimeAssembly-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754352470116566, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352470117106, "dur":633806, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\mscorlib.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352470116987, "dur":633977, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754352470750966, "dur":1065, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352470752831, "dur":210487, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ClothModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352470752394, "dur":210926, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ClothModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352470963326, "dur":778, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352470965053, "dur":365607, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352470964119, "dur":366574, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Xml.Linq-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754352471330694, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352471331178, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352471331584, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754352471331649, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352471332457, "dur":142318, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.Physics2DModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352471332181, "dur":142632, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754352471474814, "dur":250, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352471475561, "dur":108636, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352471475558, "dur":108640, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352471584199, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352471584497, "dur":166563, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352471584348, "dur":166714, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352471751063, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352471751269, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352471751608, "dur":148531, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352471751606, "dur":148535, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352471900142, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352471900295, "dur":207398, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469142779, "dur":12453, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469155239, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469155866, "dur":956, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469156822, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469157430, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469158098, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469158769, "dur":927, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469159696, "dur":751, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469160447, "dur":747, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469161194, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469161969, "dur":1040, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469163009, "dur":982, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469163991, "dur":724, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469164716, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469165326, "dur":824, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469166151, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469166838, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469167431, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469168100, "dur":868, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469168969, "dur":650, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469169619, "dur":806, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469155238, "dur":15187, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469170426, "dur":802, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.TvOS.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469170426, "dur":4455, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469174881, "dur":5418, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469180711, "dur":940, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469181652, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\Microsoft.Win32.Registry.AccessControl.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469180300, "dur":4460, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469184760, "dur":3038, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469187835, "dur":672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469188514, "dur":815, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469189338, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469189986, "dur":724, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469190718, "dur":790, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469191519, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469192161, "dur":820, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469192987, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469193702, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469194429, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469195147, "dur":733, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469195888, "dur":861, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469196757, "dur":668, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469197445, "dur":732, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469198182, "dur":275, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":7, "ts":1754352469198458, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469198662, "dur":310, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469198973, "dur":326, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469199308, "dur":235, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469200881, "dur":84360, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":7, "ts":1754352469285452, "dur":6578, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1" }}
,{ "pid":12345, "tid":7, "ts":1754352469292621, "dur":18503, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0" }}
,{ "pid":12345, "tid":7, "ts":1754352469311296, "dur":657352, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352469969474, "dur":43952, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352469968649, "dur":44779, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352470013429, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352470013763, "dur":114464, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.NVIDIAModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352470013642, "dur":114632, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.NVIDIAModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754352470128275, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352470128852, "dur":165316, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.MultiplayerModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352470128717, "dur":165498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MultiplayerModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754352470294217, "dur":278, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352470294947, "dur":302101, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.InputForUIModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352470294794, "dur":302301, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754352470597096, "dur":902, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352470598619, "dur":199880, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352470598467, "dur":200075, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754352470798543, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352470799049, "dur":386832, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352470798939, "dur":386984, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754352471185924, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352471186529, "dur":75345, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352471186383, "dur":75492, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352471261876, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352471262162, "dur":318731, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352471262028, "dur":318908, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754352471580937, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352471583638, "dur":960, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352471583453, "dur":1145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352471584599, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352471584879, "dur":146524, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352471584728, "dur":146676, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352471731406, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352471731700, "dur":168439, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352471731572, "dur":168608, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754352471900181, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352471900602, "dur":207092, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352469142814, "dur":12426, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352469155248, "dur":1170, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469156419, "dur":914, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469157333, "dur":703, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469158036, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469158659, "dur":901, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469159560, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469160453, "dur":780, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469161233, "dur":776, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469162010, "dur":904, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469162914, "dur":897, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469163812, "dur":793, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469164605, "dur":832, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469165437, "dur":865, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469166303, "dur":817, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469167120, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469167707, "dur":966, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469168673, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469169325, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469170103, "dur":849, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469170952, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469155248, "dur":16213, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352469171462, "dur":525, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.CompilerServices.SymbolWriter.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469172435, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.CSharp.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469173717, "dur":751, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Btls.Interface.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469174468, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Web.Infrastructure.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469175042, "dur":883, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.VisualC.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469176324, "dur":512, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469178668, "dur":816, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\ICSharpCode.SharpZipLib.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469179931, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469171461, "dur":9834, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352469181295, "dur":2963, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352469184258, "dur":3536, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352469187804, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":8, "ts":1754352469187862, "dur":721, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352469188599, "dur":175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\boot.config" }}
,{ "pid":12345, "tid":8, "ts":1754352469188789, "dur":166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":8, "ts":1754352469189086, "dur":699, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469189791, "dur":242, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469190039, "dur":451, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469190492, "dur":615, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469191109, "dur":210, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469191325, "dur":870, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469192197, "dur":215, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469192417, "dur":202, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469192625, "dur":902, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469193532, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469193753, "dur":186, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469193941, "dur":197, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469194140, "dur":343, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469194485, "dur":208, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469194695, "dur":171, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469194871, "dur":394, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469195268, "dur":932, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469196203, "dur":181, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469196389, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469196588, "dur":183, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469196778, "dur":343, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469197124, "dur":214, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469197340, "dur":2502, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469199844, "dur":240, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469200090, "dur":307, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469200399, "dur":523, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469200923, "dur":296, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469201221, "dur":268, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469201495, "dur":761, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469202258, "dur":571, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469202831, "dur":279, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469203112, "dur":358, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469188588, "dur":15580, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":8, "ts":1754352469205626, "dur":115, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352469206887, "dur":238731, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":8, "ts":1754352469500126, "dur":152, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":8, "ts":1754352469500010, "dur":269, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":8, "ts":1754352469500324, "dur":1499, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":8, "ts":1754352469501825, "dur":466850, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352469969599, "dur":73382, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352469968676, "dur":74346, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754352470043023, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352470043364, "dur":124, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754352470043902, "dur":123812, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352470043729, "dur":123987, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352470167718, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352470168038, "dur":68, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352470168364, "dur":178698, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AssetBundleModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352470168110, "dur":178997, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754352470347140, "dur":39442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352470387452, "dur":58488, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SpriteMaskModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352470387302, "dur":58675, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteMaskModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754352470445978, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352470446515, "dur":437348, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352470446394, "dur":437470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352470883865, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352470884019, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352470884248, "dur":468, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352470885030, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352470885291, "dur":151474, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Transactions.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352470885158, "dur":151609, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Transactions.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471036769, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352471036936, "dur":52, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Transactions.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471037622, "dur":148772, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471036990, "dur":149410, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471186400, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352471186704, "dur":194250, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Configuration.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471186547, "dur":194409, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Configuration.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471380957, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352471381115, "dur":214, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Configuration.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471384916, "dur":52863, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.StreamingModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471381341, "dur":56481, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.StreamingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754352471437823, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352471438450, "dur":79, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.StreamingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754352471439090, "dur":50540, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471438944, "dur":50688, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471489633, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352471489785, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352471489932, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352471490625, "dur":615316, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Xml.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352471490082, "dur":615860, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Xml.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352472105943, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352472106099, "dur":1616, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469142839, "dur":12409, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469155255, "dur":702, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469155958, "dur":1190, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469157149, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469157777, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469158330, "dur":913, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469159243, "dur":785, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469160028, "dur":785, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469160813, "dur":752, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469161565, "dur":1041, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469162606, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469163345, "dur":966, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469164311, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469164826, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469165568, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469166257, "dur":697, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469166954, "dur":641, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469167595, "dur":903, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469168499, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469169097, "dur":768, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469169865, "dur":902, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469155255, "dur":15513, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469170768, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Analytics.deps.json" }}
,{ "pid":12345, "tid":9, "ts":1754352469170768, "dur":3226, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469173995, "dur":4971, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469180409, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469178967, "dur":5892, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469184859, "dur":2952, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469187833, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469188547, "dur":908, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469189461, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469190111, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469190831, "dur":738, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469191576, "dur":682, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469192264, "dur":860, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469193133, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469193736, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469194412, "dur":705, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469195123, "dur":674, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469195804, "dur":817, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469196627, "dur":726, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469197358, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469197890, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":9, "ts":1754352469198033, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469198392, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":9, "ts":1754352469198526, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469198718, "dur":185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":9, "ts":1754352469198904, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469208021, "dur":112063, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":9, "ts":1754352469320310, "dur":648358, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352469969580, "dur":133520, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352469968675, "dur":134485, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754352470103161, "dur":1119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352470104281, "dur":65, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754352470104641, "dur":105456, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352470104637, "dur":105504, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression.FileSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754352470210143, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352470210792, "dur":137283, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.MarshallingModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352470210669, "dur":137446, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MarshallingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754352470348116, "dur":38325, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352470387067, "dur":276259, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352470386797, "dur":276970, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754352470663769, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352470664698, "dur":194774, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352470664492, "dur":194981, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352470859474, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352470859738, "dur":201264, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualScripting.Antlr3.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352470859624, "dur":201768, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Antlr3.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754352471061393, "dur":296, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352471062082, "dur":118597, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471061967, "dur":118713, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471180682, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352471180985, "dur":77689, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.GIModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471180837, "dur":77839, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GIModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471258677, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352471258949, "dur":151689, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471258827, "dur":151852, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Tayx.Graphy-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754352471410680, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352471410849, "dur":144, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Tayx.Graphy-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754352471411714, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352471411872, "dur":8226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352471420099, "dur":138, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471420504, "dur":33257, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471420250, "dur":33513, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471453764, "dur":3770, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352471457784, "dur":1420, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471457628, "dur":1577, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Recorder.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471459206, "dur":227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352471459598, "dur":439705, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Drawing.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471459458, "dur":439847, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Drawing.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352471899307, "dur":266, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352471899585, "dur":208101, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469142876, "dur":12381, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469155264, "dur":859, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469156124, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469156845, "dur":774, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469157619, "dur":636, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469158256, "dur":746, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469159002, "dur":1088, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469160090, "dur":770, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469160860, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469161617, "dur":1165, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469162782, "dur":939, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469163721, "dur":989, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469164710, "dur":715, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469165426, "dur":801, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469166230, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469166895, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469167522, "dur":881, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469168403, "dur":615, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469169018, "dur":751, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469169769, "dur":914, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469170683, "dur":511, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469155264, "dur":15930, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469172759, "dur":511, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Debugger.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469174261, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469174872, "dur":832, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469176599, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.Formatting.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469179952, "dur":607, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469171194, "dur":9365, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469180560, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469180560, "dur":3738, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469184298, "dur":3498, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469187825, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469188489, "dur":767, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469189279, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469189916, "dur":703, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469190632, "dur":792, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469191430, "dur":717, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469192154, "dur":783, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469192946, "dur":644, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469193598, "dur":677, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469194282, "dur":702, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469194992, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469195657, "dur":723, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469196388, "dur":808, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469197204, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469197698, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":10, "ts":1754352469197868, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469198057, "dur":337, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":10, "ts":1754352469198394, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469198570, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469198785, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469200632, "dur":360185, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS" }}
,{ "pid":12345, "tid":10, "ts":1754352469560935, "dur":407716, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352469969486, "dur":102400, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352469968666, "dur":103223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352470071891, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470072357, "dur":165522, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352470072213, "dur":165668, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352470237882, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470238179, "dur":306621, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352470238038, "dur":306806, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754352470544845, "dur":283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470545538, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470545806, "dur":345823, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352470545676, "dur":345997, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.ServiceModel.Internals-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754352470891674, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470892241, "dur":23666, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352470892112, "dur":23796, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352470915910, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470916068, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470916292, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470916410, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470916609, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470916785, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352470917329, "dur":102238, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352470917210, "dur":102406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754352471019617, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471020149, "dur":90726, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ARModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471020039, "dur":90838, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ARModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471110879, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471111245, "dur":90360, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471111044, "dur":90562, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471201607, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471201857, "dur":170500, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471201739, "dur":170655, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754352471372395, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471372885, "dur":41733, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SubstanceModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471372779, "dur":41879, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SubstanceModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754352471414659, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471415121, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471415239, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471415369, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471415495, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471415602, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471415710, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471415819, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471415927, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471416034, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471416295, "dur":206048, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471416183, "dur":206161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471622345, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471622646, "dur":62010, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ImageConversionModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471622497, "dur":62193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ImageConversionModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754352471684691, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471685237, "dur":262, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471685515, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471685813, "dur":1875, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Collections.LowLevel.ILSupport.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471685675, "dur":2045, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754352471687721, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471688219, "dur":176914, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471688101, "dur":177070, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754352471865172, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471865722, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471865872, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471866010, "dur":289, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471866310, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471866500, "dur":262, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471866773, "dur":70449, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352471866771, "dur":70493, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Multiplayer.Center.Common-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754352471937266, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352471937821, "dur":169880, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469142909, "dur":12357, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469155292, "dur":880, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469156172, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469156911, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469157537, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469158197, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469158898, "dur":971, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469159869, "dur":784, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469160653, "dur":796, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469161449, "dur":1053, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469162502, "dur":745, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469163247, "dur":993, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469164241, "dur":565, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469164807, "dur":825, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469165633, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469166277, "dur":836, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469167114, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469167770, "dur":960, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469168730, "dur":727, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469169457, "dur":845, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469170303, "dur":815, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469155292, "dur":15827, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":****************, "dur":504, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":11, "ts":****************, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Routing.dll" }}
,{ "pid":12345, "tid":11, "ts":****************, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Discovery.dll" }}
,{ "pid":12345, "tid":11, "ts":****************, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Activation.dll" }}
,{ "pid":12345, "tid":11, "ts":****************, "dur":812, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Security.dll" }}
,{ "pid":12345, "tid":11, "ts":****************, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.Formatters.Soap.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469180278, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.PlatformServices.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469171119, "dur":9688, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469181315, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.ScreenCaptureModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469180807, "dur":5198, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469186005, "dur":1839, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469187849, "dur":810, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469188667, "dur":856, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469189535, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469190201, "dur":754, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469190961, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469191678, "dur":698, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469192382, "dur":757, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469193144, "dur":459, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469193610, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469194206, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469194924, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469195553, "dur":697, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469196258, "dur":825, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469197091, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469197687, "dur":317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":11, "ts":1754352469198004, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469198147, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":11, "ts":1754352469198284, "dur":481, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469200601, "dur":84615, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets2.assets" }}
,{ "pid":12345, "tid":11, "ts":1754352469285441, "dur":5263, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1.resS" }}
,{ "pid":12345, "tid":11, "ts":1754352469290906, "dur":19859, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0.resS" }}
,{ "pid":12345, "tid":11, "ts":1754352469310976, "dur":657670, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352469969504, "dur":145281, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352469968647, "dur":146140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352470114789, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352470115225, "dur":576381, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352470114950, "dur":576662, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352470691614, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352470691943, "dur":182647, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352470691822, "dur":182769, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352470874592, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352470874763, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352470875145, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352470875465, "dur":50611, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ClusterInputModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352470875463, "dur":50652, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterInputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754352470926116, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352470926610, "dur":249, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352470927334, "dur":186582, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352470927185, "dur":186774, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Net.Http-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754352471113960, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471114497, "dur":82046, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471114388, "dur":82156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471196545, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471196725, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471197202, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471197441, "dur":205021, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Data.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471197316, "dur":205188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Data-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754352471402505, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471403205, "dur":68125, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471402966, "dur":68406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.State-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754352471471373, "dur":218, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471471832, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471471973, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471472129, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471472266, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471472438, "dur":143246, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471472436, "dur":143250, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Burst.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471615688, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471615960, "dur":1090, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471615833, "dur":1218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471617051, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471617223, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471617620, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471617861, "dur":126068, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471617730, "dur":126237, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754352471743968, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471744581, "dur":123149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471744459, "dur":123310, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754352471867775, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352471868551, "dur":238352, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Xml.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352471868548, "dur":238396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754352472106945, "dur":338, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352472107534, "dur":163, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469142940, "dur":12332, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469155273, "dur":800, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469156073, "dur":986, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469157059, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469157737, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469158448, "dur":980, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469159429, "dur":905, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_7.0.1323.51816.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469160334, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469161107, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469161865, "dur":1177, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469163042, "dur":1039, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469164081, "dur":735, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469164816, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469165416, "dur":834, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469166250, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469166942, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469167601, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469168494, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469169111, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469169886, "dur":872, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469170758, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":12, "ts":1754352469155273, "dur":16001, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469172798, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.DirectoryServices.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469174286, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Deployment.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469174961, "dur":912, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469176308, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Services.Client.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469180123, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469171274, "dur":9492, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469181342, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469180766, "dur":5159, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469185925, "dur":1959, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469187886, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469188603, "dur":826, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469189435, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469190102, "dur":795, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469190905, "dur":732, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469191643, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469192291, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469193141, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469193643, "dur":698, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469194349, "dur":723, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469195080, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469195733, "dur":834, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469196574, "dur":701, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469197295, "dur":776, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469198076, "dur":525, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":12, "ts":1754352469198602, "dur":465, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469209008, "dur":83245, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":12, "ts":1754352469292746, "dur":25685, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":12, "ts":1754352469318557, "dur":650178, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352469969415, "dur":94736, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352469968736, "dur":95459, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Data.DataSetExtensions-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754352470064196, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352470064734, "dur":272456, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TerrainPhysicsModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352470064607, "dur":272584, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TerrainPhysicsModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352470337193, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352470337599, "dur":455441, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.GameCenterModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352470337339, "dur":455745, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GameCenterModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754352470793086, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352470794309, "dur":122118, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ClusterInputModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352470794131, "dur":122302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ClusterInputModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352470916433, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352470916599, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352470916786, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352470917239, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352470917740, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352470918357, "dur":92288, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352470918198, "dur":92449, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471010649, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471010971, "dur":184870, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471010819, "dur":185064, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Newtonsoft.Json-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754352471195885, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471196294, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471196800, "dur":384117, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471196667, "dur":384281, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754352471580949, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471581539, "dur":97250, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.EnterpriseServices.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471581412, "dur":97379, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.EnterpriseServices.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471678793, "dur":2108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471680924, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471681095, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471681250, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471681406, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471681544, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471681687, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471681863, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471682024, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471682229, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471682434, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471682881, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471684239, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471684788, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471685525, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471686055, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471686499, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471686868, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471687395, "dur":176020, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471687239, "dur":176220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualEffectGraph.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754352471863460, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471863912, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471864055, "dur":1010, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471864052, "dur":1018, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471865071, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471865214, "dur":6716, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471865212, "dur":6719, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352471871932, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471872497, "dur":308, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471872813, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471873038, "dur":2586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471875731, "dur":293, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471876319, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471877221, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471878069, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471878613, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471879012, "dur":1539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352471880552, "dur":1057, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestAssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754352471882006, "dur":225679, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352472122650, "dur":5152, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 54635968, "ts": 1754352472136972, "dur": 24, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "unitylinker_dwek.traceevents"} },
{ "pid": 93864, "tid": 54635968, "ts": 1754352472138590, "dur": 23741, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 54635968, "ts": 1754352472137189, "dur": 1400, "ph": "X", "name": "unitylinker_dwek.traceevents", "args": {} },
{ "pid": 93864, "tid": 54635968, "ts": 1754352472162583, "dur": 5988, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 54635968, "ts": 1754352472130654, "dur": 37965, "ph": "X", "name": "Write chrome-trace events", "args": {} },
