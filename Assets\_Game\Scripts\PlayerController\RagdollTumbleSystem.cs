using UnityEngine;
using System.Collections;
using System.Text;
using KinematicCharacterController;
using KinematicCharacterController.FPS;

public class RagdollTumbleSystem : MonoBehaviour
{
    [Header("References")]
    public FPSCharacterController playerController;
    public GameObject ragdollObject;
    public GameObject playerModel;
    public Camera playerCamera;
    public Camera ragdollCamera;
    public GameObject viewportUI;
    public FallDamageSystem fallDamageSystem;
    public FPSCharacterCamera cameraController;
    public PlayerStatus playerStatus;

    [Header("Tumble Settings")]
    public float maxTumbleDuration = 5.0f;
    public float recoveryDuration = 1.0f;
    public float tumbleForceMultiplier = 5.0f;
    public float tumbleTorqueMultiplier = 1.5f;
    public float ragdollVerticalOffset = 0.9f;

    [Header("Auto-Recovery Settings")]
    public float stoppedVelocityThreshold = 0.05f; // Very lenient - only trigger when truly stopped
    public float stoppedDurationThreshold = 3.0f; // Must be stopped for 3 full seconds
    public float minTumbleDuration = 3.0f; // Minimum 3 seconds before any auto-recovery
    public bool autoRecoverOnStop = true;

    [Header("Layers")]
    public int ragdollLayer = 8;

    [Header("Debug")]
    public bool debugLog = true;
    public bool debugPhysics = false;
    public bool debugCollisions = false;
    public bool generateDebugReport = false;
    public float physicsDebugInterval = 0.5f;

    [Header("Camera Settings")]
    public float cameraTransitionSpeed = 5.0f;

    [Header("Anti-Clipping Settings")]
    public float overlapCheckRadius = 0.5f;
    public float safeRepositionDistance = 0.1f;
    public int maxRepositionAttempts = 5;
    public LayerMask geometryLayers;

    [Header("Ground Safety Settings")]
    public float minGroundSlopeUpwardness = 0.6f; // Minimum surface angle (0.6 = ~53 degrees)
    public float groundThicknessCheckDistance = 2.0f; // How deep to check for solid ground
    public float platformSupportRadius = 1.0f; // Required radius of solid ground around player
    public int minPlatformSupportPoints = 6; // Out of 8 total check points
    public float horizontalClearanceRadius = 0.8f; // Required clearance to avoid crevasses
    public float fallSimulationGroundCheckDistance = 0.5f; // How far down to check for ground support
    public int minSupportDirections = 6; // Out of 8 directions for platform edge detection

    private bool isTumbling = false;
    public bool IsTumbling => isTumbling;
    private float tumbleTimer = 0f;
    private bool isRecovering = false;
    private float recoveryTimer = 0f;
    private Vector3 initialPosition;
    private Quaternion initialRotation;
    private Vector3 recoveryStartPosition;
    private Quaternion recoveryStartRotation;
    private Rigidbody ragdollRigidbody;
    private Rigidbody[] ragdollRigidbodies;
    private Collider[] playerColliders;
    private Collider[] ragdollColliders;
    private KinematicCharacterMotor playerMotor;
    private CharacterController characterController;
    private Vector3 lastFallVelocity;
    private float lastPhysicsDebugTime = 0f;
    private StringBuilder debugReportBuilder = new StringBuilder();
    private int collisionCount = 0;
    private float belowThresholdTime = 0f;
    private Vector3 lastRagdollPosition;
    private bool hasInitializedStopDetection = false;
    private float originalLookSpeed;
    private bool isCameraTransitioning = false;
    private float cameraTransitionProgress = 0f;
    private Vector3 originalCameraPosition;
    private Quaternion originalCameraRotation;
    private float tumbleCooldownTimer = 0f;
    private const float TUMBLE_COOLDOWN = 2.0f;
    private Vector3 lastValidRagdollPosition; // NEW: For clipping correction
    private Coroutine clippingMonitorCoroutine; // NEW: Monitor for clipping

    private void Awake()
    {
        LogInfo("RagdollTumbleSystem Awake");

        if (ragdollObject != null)
        {
            ragdollObject.SetActive(false);

            ragdollRigidbodies = ragdollObject.GetComponentsInChildren<Rigidbody>(true);
            if (ragdollRigidbodies.Length > 0)
            {
                ragdollRigidbody = ragdollRigidbodies[0];
                
                // Configure ragdoll rigidbodies with better anti-clipping
                foreach (Rigidbody rb in ragdollRigidbodies)
                {
                    if (rb != null)
                    {
                        rb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic; // Prevent clipping
                        rb.maxLinearVelocity = 30f; // Slightly higher to allow natural falls
                        rb.maxAngularVelocity = 40f;
                        
                        if (rb.GetComponent<Collider>() != null)
                        {
                            Collider rbCollider = rb.GetComponent<Collider>();
                            
                            if (rbCollider.material == null)
                            {
                                PhysicsMaterial ragdollMaterial = new PhysicsMaterial("RagdollMaterial");
                                ragdollMaterial.dynamicFriction = 0.6f;
                                ragdollMaterial.staticFriction = 0.8f;
                                ragdollMaterial.bounciness = 0.1f;
                                ragdollMaterial.frictionCombine = PhysicsMaterialCombine.Average;
                                ragdollMaterial.bounceCombine = PhysicsMaterialCombine.Minimum;
                                
                                rbCollider.material = ragdollMaterial;
                            }
                        }
                    }
                }
            }

            ragdollColliders = ragdollObject.GetComponentsInChildren<Collider>(true);

            if (debugLog && debugCollisions && ragdollColliders.Length > 0)
                {
                    foreach (Collider col in ragdollColliders)
                    {
                        if (col != null && col.gameObject != null)
                        {
                            CollisionDebugger debugger = col.gameObject.AddComponent<CollisionDebugger>();
                            debugger.tumbleSystem = this;
                        }
                    }
                }
        }

        playerColliders = GetComponentsInChildren<Collider>(true);

        if (playerController != null)
        {
            playerMotor = playerController.GetComponent<KinematicCharacterMotor>();
            characterController = GetComponent<CharacterController>();
        }

        if (cameraController == null && playerCamera != null)
        {
            cameraController = playerCamera.GetComponent<FPSCharacterCamera>();
            if (cameraController == null)
            {
                cameraController = playerCamera.GetComponentInParent<FPSCharacterCamera>();
            }
        }

        if (ragdollCamera != null)
        {
            ragdollCamera.enabled = false;
        }

        if (fallDamageSystem == null)
        {
            fallDamageSystem = GetComponent<FallDamageSystem>();
        }

        if (playerStatus == null)
        {
            playerStatus = GetComponent<PlayerStatus>();
        }

        ragdollLayer = Mathf.Clamp(ragdollLayer, 0, 31);
        maxTumbleDuration = Mathf.Max(maxTumbleDuration, 5.0f);

        // Configure geometry layers if not set
        if (geometryLayers.value == 0)
        {
            geometryLayers = Physics.AllLayers;
            if (ragdollLayer > 0)
            {
                geometryLayers &= ~(1 << ragdollLayer);
            }
            int playerLayer = LayerMask.NameToLayer("Player");
            if (playerLayer >= 0)
            {
                geometryLayers &= ~(1 << playerLayer);
            }
        }
    }

    private void OnEnable()
    {
        if (fallDamageSystem != null)
        {
            fallDamageSystem.OnTumbleRequired += HandleTumbleRequest;
        }
    }

    private void OnDisable()
    {
        if (fallDamageSystem != null)
        {
            fallDamageSystem.OnTumbleRequired -= HandleTumbleRequest;
        }
    }

    private bool IsPlayerDead()
    {
        if (playerStatus != null)
        {
            return playerStatus.CurrentHits >= playerStatus.MaxHits;
        }
        return false;
    }

    private void HandleTumbleRequest(float impactVelocity, Vector3 horizontalVelocity)
    {
        if (isTumbling || IsPlayerDead() || tumbleCooldownTimer > 0)
        {
            return;
        }

        StartTumble(impactVelocity, horizontalVelocity);
    }

    public void StartTumble(float impactVelocity, Vector3 horizontalVelocity)
    {
        if (isTumbling || ragdollObject == null || IsPlayerDead() || tumbleCooldownTimer > 0)
        {
            return;
        }

        debugReportBuilder.Clear();
        collisionCount = 0;
        hasInitializedStopDetection = false;
        belowThresholdTime = 0f;

        initialPosition = playerController.transform.position;
        initialRotation = playerController.transform.rotation;

        Vector3 ragdollPosition = initialPosition;
        ragdollPosition.y += ragdollVerticalOffset;

        ragdollObject.transform.position = ragdollPosition;
        ragdollObject.transform.rotation = initialRotation;

        lastValidRagdollPosition = ragdollPosition; // NEW: Initialize last valid position

        if (playerCamera != null)
        {
            originalCameraPosition = playerCamera.transform.position;
            originalCameraRotation = playerCamera.transform.rotation;
        }

        DisablePlayerControl();

        if (playerModel != null)
        {
            playerModel.SetActive(false);
        }

        ragdollObject.SetActive(true);

        foreach (Collider col in ragdollColliders)
        {
            if (col != null)
            {
                col.enabled = true;
                col.gameObject.layer = ragdollLayer;
            }
        }

        ApplyTumbleForces(impactVelocity, horizontalVelocity);
        SwitchCameras(true);

        isTumbling = true;
        isRecovering = false;
        tumbleTimer = 0f;
        recoveryTimer = 0f;

        // NEW: Start lightweight clipping monitor
        if (clippingMonitorCoroutine != null)
        {
            StopCoroutine(clippingMonitorCoroutine);
        }
        clippingMonitorCoroutine = StartCoroutine(MonitorForClipping());

        LogInfo($"Started tumbling with impact velocity: {impactVelocity}");
    }

    private IEnumerator MonitorForClipping()
    {
        float checkInterval = 0.02f; // Reduced for more frequent checks to catch slow penetrations
        while (isTumbling && !isRecovering)
        {
            yield return new WaitForSeconds(checkInterval);
            
            if (ragdollObject == null) yield break;
            
            Vector3 currentPosition = ragdollObject.transform.position;
            
            // IMPROVED: Raycast-based clipping detection
            if (DetectWallClipping(lastValidRagdollPosition, currentPosition))
            {
                // Ragdoll clipped through a wall - correct it
                CorrectRagdollClipping(currentPosition);
            }
            else
            {
                // Position is valid, update last valid position
                lastValidRagdollPosition = currentPosition;
            }
    
            // NEW: Additional penetration resolution to handle slow overlaps
            ResolvePenetrations();
        }
    }

    private bool DetectWallClipping(Vector3 fromPosition, Vector3 toPosition)
    {
        Vector3 direction = toPosition - fromPosition;
        float distance = direction.magnitude;
        
        // Skip check if positions are too close (avoid false positives)
        if (distance < 0.1f) 
        {
            return false;
        }
        
        direction.Normalize();
        
        // Raycast from last valid position to current position
        RaycastHit hit;
        if (Physics.Raycast(fromPosition, direction, out hit, distance, geometryLayers.value))
        {
            // We hit something between the last valid position and current position
            // This means the ragdoll clipped through it
            
            // Additional check: make sure we actually moved past the hit point
            float distanceToHit = Vector3.Distance(fromPosition, hit.point);
            float distanceMoved = Vector3.Distance(fromPosition, toPosition);
            
            if (distanceMoved > distanceToHit + 0.1f) // We moved past the collision point
            {
                LogInfo($"WALL CLIPPING DETECTED: Hit {hit.collider.name} at {hit.point}, distance to hit: {distanceToHit:F2}m, distance moved: {distanceMoved:F2}m");
                return true;
            }
        }
        
        // Additional check: Multi-point raycast for more thorough detection
        // Check from multiple points around the ragdoll (torso, limbs) to catch partial clipping
        Vector3[] checkOffsets = new Vector3[]
        {
            Vector3.zero,           // Center
            Vector3.up * 0.5f,      // Upper torso
            Vector3.down * 0.5f,    // Lower torso
            Vector3.left * 0.3f,    // Left side
            Vector3.right * 0.3f,   // Right side
            Vector3.forward * 0.3f, // Front
            Vector3.back * 0.3f     // Back
        };
        
        foreach (Vector3 offset in checkOffsets)
        {
            Vector3 checkFrom = fromPosition + offset;
            Vector3 checkTo = toPosition + offset;
            Vector3 checkDirection = (checkTo - checkFrom).normalized;
            float checkDistance = Vector3.Distance(checkFrom, checkTo);
            
            if (checkDistance < 0.1f) continue;
            
            if (Physics.Raycast(checkFrom, checkDirection, out hit, checkDistance, geometryLayers.value))
            {
                float distanceToHit = Vector3.Distance(checkFrom, hit.point);
                if (checkDistance > distanceToHit + 0.1f)
                {
                    LogInfo($"PARTIAL CLIPPING DETECTED: Offset {offset} hit {hit.collider.name} at {hit.point}");
                    return true;
                }
            }
        }
        
        return false;
    }

    private void CorrectRagdollClipping(Vector3 clippedPosition)
    {
        // Find the exact point where we should stop (just before the wall)
        Vector3 direction = (clippedPosition - lastValidRagdollPosition).normalized;
        float searchDistance = Vector3.Distance(lastValidRagdollPosition, clippedPosition);
        
        RaycastHit hit;
        Vector3 correctionPosition = lastValidRagdollPosition;
        
        if (Physics.Raycast(lastValidRagdollPosition, direction, out hit, searchDistance, geometryLayers.value))
        {
            // Position ragdoll just before the wall (with small safety margin)
            correctionPosition = hit.point - (direction * 0.2f);
            LogInfo($"Correcting clipping: Moving from {clippedPosition} to {correctionPosition} (hit {hit.collider.name})");
        }
        else
        {
            // Fallback: use last valid position with slight upward offset
            correctionPosition = lastValidRagdollPosition + Vector3.up * 0.3f;
            LogInfo($"Clipping correction fallback: Moving to {correctionPosition}");
        }
        
        // Apply the correction
        ragdollObject.transform.position = correctionPosition;
        
        // Reduce velocity to prevent immediate re-clipping
        if (ragdollRigidbodies != null)
        {
            foreach (Rigidbody rb in ragdollRigidbodies)
            {
                if (rb != null)
                {
                    // Reduce velocity but don't stop it completely (keep some momentum)
                    rb.linearVelocity *= 0.3f; // More aggressive reduction
                    rb.angularVelocity *= 0.3f;
                    
                    // Add a small outward force to push away from the wall
                    if (hit.normal != Vector3.zero)
                    {
                        rb.AddForce(hit.normal * 2f, ForceMode.Impulse);
                    }
                }
            }
        }
        else if (ragdollRigidbody != null)
        {
            ragdollRigidbody.linearVelocity *= 0.3f;
            ragdollRigidbody.angularVelocity *= 0.3f;
            
            if (hit.normal != Vector3.zero)
            {
                ragdollRigidbody.AddForce(hit.normal * 2f, ForceMode.Impulse);
            }
        }
        
        // Update last valid position
        lastValidRagdollPosition = correctionPosition;
    }

    private void ApplyTumbleForces(float impactVelocity, Vector3 horizontalVelocity)
    {
        Vector3 forceDirection = Vector3.down;

        if (horizontalVelocity.magnitude > 0.1f)
        {
            forceDirection = (horizontalVelocity.normalized * 0.7f) + (Vector3.down * 0.3f);
            forceDirection.Normalize();
        }

        if (ragdollRigidbodies != null && ragdollRigidbodies.Length > 0)
        {
            foreach (Rigidbody rb in ragdollRigidbodies)
            {
                if (rb != null)
                {
                    rb.isKinematic = false;
                    rb.useGravity = true;
                    rb.linearVelocity = Vector3.zero;
                    rb.angularVelocity = Vector3.zero;

                    Vector3 appliedForce = forceDirection * impactVelocity * tumbleForceMultiplier;
                    if (appliedForce.magnitude > 30f * rb.mass) // Match new max velocity
                    {
                        appliedForce = appliedForce.normalized * (30f * rb.mass * 0.8f);
                    }
                    
                    rb.AddForce(appliedForce, ForceMode.Impulse);

                    Vector3 torque = new Vector3(
                        UnityEngine.Random.Range(-1f, 1f),
                        UnityEngine.Random.Range(-1f, 1f),
                        UnityEngine.Random.Range(-1f, 1f)) * impactVelocity * tumbleTorqueMultiplier;

                    if (torque.magnitude > 50f)
                    {
                        torque = torque.normalized * 50f;
                    }

                    rb.AddTorque(torque, ForceMode.Impulse);
                }
            }
        }
        else if (ragdollRigidbody != null)
        {
            ragdollRigidbody.isKinematic = false;
            ragdollRigidbody.useGravity = true;
            ragdollRigidbody.linearVelocity = Vector3.zero;
            ragdollRigidbody.angularVelocity = Vector3.zero;

            Vector3 appliedForce = forceDirection * impactVelocity * tumbleForceMultiplier;
            if (appliedForce.magnitude > 30f * ragdollRigidbody.mass)
            {
                appliedForce = appliedForce.normalized * (30f * ragdollRigidbody.mass * 0.8f);
            }

            ragdollRigidbody.AddForce(appliedForce, ForceMode.Impulse);

            Vector3 torque = new Vector3(
                UnityEngine.Random.Range(-1f, 1f),
                UnityEngine.Random.Range(-1f, 1f),
                UnityEngine.Random.Range(-1f, 1f)) * impactVelocity * tumbleTorqueMultiplier;

            if (torque.magnitude > 50f)
            {
                torque = torque.normalized * 50f;
            }

            ragdollRigidbody.AddTorque(torque, ForceMode.Impulse);
        }
    }

    private void DisablePlayerControl()
    {
        if (playerController != null)
        {
            playerController.enabled = false;
        }

        if (cameraController != null)
        {
            originalLookSpeed = cameraController.LookSpeed;
            cameraController.LookSpeed = 0f;
        }

        if (playerMotor != null)
        {
            playerMotor.enabled = false;
        }

        if (characterController != null)
        {
            characterController.enabled = false;
        }

        if (playerColliders != null)
        {
            foreach (Collider col in playerColliders)
            {
                if (col != null)
                {
                    col.enabled = false;
                }
            }
        }

        if (viewportUI != null)
        {
            viewportUI.SetActive(false);
        }
    }

    private void EnablePlayerControl()
    {
        if (playerController != null)
        {
            playerController.enabled = true;
        }

        if (cameraController != null)
        {
            cameraController.LookSpeed = originalLookSpeed;
        }

        if (playerMotor != null)
        {
            playerMotor.enabled = true;
        }

        if (characterController != null)
        {
            characterController.enabled = true;
        }

        if (playerColliders != null)
        {
            foreach (Collider col in playerColliders)
            {
                if (col != null)
                {
                    col.enabled = true;
                }
            }
        }

        if (playerModel != null)
        {
            playerModel.SetActive(true);
        }

        if (viewportUI != null)
        {
            viewportUI.SetActive(true);
        }
    }

    private void SwitchCameras(bool toRagdoll)
    {
        if (playerCamera != null && ragdollCamera != null)
        {
            if (toRagdoll)
            {
                playerCamera.enabled = false;
                ragdollCamera.enabled = true;
                isCameraTransitioning = false;
                cameraTransitionProgress = 0f;
            }
            else
            {
                // Immediately restore original camera state
                playerCamera.transform.position = originalCameraPosition;
                playerCamera.transform.rotation = originalCameraRotation;
                
                playerCamera.enabled = true;
                ragdollCamera.enabled = false;
                
                isCameraTransitioning = false;
                cameraTransitionProgress = 0f;
            }
        }
    }

    private void Update()
    {
        if (tumbleCooldownTimer > 0)
        {
            tumbleCooldownTimer -= Time.deltaTime;
        }

        if (isTumbling && IsPlayerDead())
        {
            ForceEndTumble();
            return;
        }

        if (!isTumbling)
            return;

        tumbleTimer += Time.deltaTime;

        if (debugPhysics && Time.time > lastPhysicsDebugTime + physicsDebugInterval)
        {
            lastPhysicsDebugTime = Time.time;
            LogPhysicsState();
        }

        if (!isRecovering)
        {
            // Simplified auto-recovery - only check if ragdoll is truly stopped for a long time
            if (autoRecoverOnStop && tumbleTimer >= minTumbleDuration)
            {
                CheckIfRagdollStopped();
            }

            if (Input.GetKeyDown(KeyCode.Space) || tumbleTimer >= maxTumbleDuration)
            {
                StartRecovery();
            }
        }
        else
        {
            UpdateRecovery();
        }
    }

    private void CheckIfRagdollStopped()
    {
        if (ragdollObject == null) return;

        if (!hasInitializedStopDetection)
        {
            lastRagdollPosition = ragdollObject.transform.position;
            hasInitializedStopDetection = true;
            belowThresholdTime = 0f;
            return;
        }

        float maxVelocity = 0f;

        // Check position change
        Vector3 positionDelta = ragdollObject.transform.position - lastRagdollPosition;
        float positionDeltaMagnitude = positionDelta.magnitude / Time.deltaTime;
        lastRagdollPosition = ragdollObject.transform.position;

        maxVelocity = positionDeltaMagnitude;

        // Check rigidbody velocities with proper bounds checking
        if (ragdollRigidbodies != null && ragdollRigidbodies.Length > 0)
        {
            for (int i = 0; i < ragdollRigidbodies.Length; i++)
            {
                if (i < ragdollRigidbodies.Length && ragdollRigidbodies[i] != null && !ragdollRigidbodies[i].isKinematic)
                {
                    try
                    {
                        float rbVelocity = ragdollRigidbodies[i].linearVelocity.magnitude;
                        maxVelocity = Mathf.Max(maxVelocity, rbVelocity);
                    }
                    catch (System.Exception e)
                    {
                        LogWarning($"Error accessing rigidbody velocity: {e.Message}");
                    }
                }
            }
        }
        else if (ragdollRigidbody != null && !ragdollRigidbody.isKinematic)
        {
            try
            {
                float rbVelocity = ragdollRigidbody.linearVelocity.magnitude;
                maxVelocity = Mathf.Max(maxVelocity, rbVelocity);
            }
            catch (System.Exception e)
            {
                LogWarning($"Error accessing main rigidbody velocity: {e.Message}");
            }
        }

        if (maxVelocity < stoppedVelocityThreshold)
        {
            belowThresholdTime += Time.deltaTime;

            if (belowThresholdTime >= stoppedDurationThreshold)
            {
                LogInfo($"Auto-recovery triggered - ragdoll stopped for {stoppedDurationThreshold} seconds");
                StartRecovery();
            }
        }
        else
        {
                belowThresholdTime = 0f;
        }
    }

    private void LogPhysicsState()
    {
        if (!isTumbling || ragdollObject == null) return;

        StringBuilder sb = new StringBuilder();
        sb.AppendLine($"=== PHYSICS STATE at {Time.time:F2}s ===");

        float totalVelocity = 0f;
        int rbCount = 0;

        if (ragdollRigidbodies != null && ragdollRigidbodies.Length > 0)
        {
            for (int i = 0; i < ragdollRigidbodies.Length; i++)
            {
                if (i < ragdollRigidbodies.Length && ragdollRigidbodies[i] != null)
                {
                    float vel = 0f;
                    try
                    {
                        vel = ragdollRigidbodies[i].linearVelocity.magnitude;
                        totalVelocity += vel;
                        rbCount++;
                    }
                    catch (System.Exception e)
                    {
                        vel = 0f;
                        LogWarning($"Error logging rigidbody {i} velocity: {e.Message}");
                    }

                    sb.AppendLine($"RB {ragdollRigidbodies[i].name}: pos={ragdollRigidbodies[i].position}, vel={vel:F2}m/s");
                }
            }
        }
        else if (ragdollRigidbody != null)
        {
            float vel = 0f;
            try
            {
                vel = ragdollRigidbody.linearVelocity.magnitude;
                totalVelocity += vel;
                rbCount++;
            }
            catch (System.Exception e)
            {
                vel = 0f;
                LogWarning($"Error logging main rigidbody velocity: {e.Message}");
            }

            sb.AppendLine($"RB {ragdollRigidbody.name}: pos={ragdollRigidbody.position}, vel={vel:F2}m/s");
        }

        if (rbCount > 0)
        {
            sb.AppendLine($"Average velocity: {totalVelocity / rbCount:F2}m/s");
        }

        LogInfo(sb.ToString());
    }

    private void StartRecovery()
    {
        if (!isTumbling || isRecovering || ragdollObject == null)
        {
            return;
        }

        isRecovering = true;
        recoveryTimer = 0f;

        recoveryStartPosition = ragdollObject.transform.position;
        recoveryStartRotation = ragdollObject.transform.rotation;

        // Freeze ragdoll physics
        if (ragdollRigidbodies != null && ragdollRigidbodies.Length > 0)
        {
            foreach (Rigidbody rb in ragdollRigidbodies)
            {
                if (rb != null)
                {
                    rb.isKinematic = true;
                }
            }
        }
        else if (ragdollRigidbody != null)
        {
            ragdollRigidbody.isKinematic = true;
        }

        // NEW: Stop clipping monitor
        if (clippingMonitorCoroutine != null)
        {
            StopCoroutine(clippingMonitorCoroutine);
            clippingMonitorCoroutine = null;
        }

        LogInfo("Started recovery from tumble");
    }

    private void UpdateRecovery()
    {
        recoveryTimer += Time.deltaTime;
        float recoveryProgress = Mathf.Clamp01(recoveryTimer / recoveryDuration);

        if (ragdollObject != null)
        {
            Vector3 targetPosition = recoveryStartPosition;
            float groundY = FindGroundHeight(recoveryStartPosition);
            
            if (groundY > 0)
            {
                targetPosition.y = groundY + 1f;
            }
            else
            {
                targetPosition.y = recoveryStartPosition.y + 1f;
            }

            ragdollObject.transform.position = Vector3.Lerp(recoveryStartPosition, targetPosition, recoveryProgress);

            Quaternion targetRotation = Quaternion.Euler(0f, recoveryStartRotation.eulerAngles.y, 0f);
            ragdollObject.transform.rotation = Quaternion.Slerp(recoveryStartRotation, targetRotation, recoveryProgress);
        }

        if (recoveryProgress >= 1.0f)
        {
            CompleteTumble();
        }
    }

    private float FindGroundHeight(Vector3 position)
    {
        float rayLength = 10f;
        RaycastHit hit;
        Vector3 rayStart = position + Vector3.up * 2f;
        
        if (Physics.Raycast(rayStart, Vector3.down, out hit, rayLength, geometryLayers.value))
        {
            // NEW: Validate that this is actually solid ground, not thin geometry
            if (ValidateGroundSolidity(hit.point, hit.normal))
            {
                return hit.point.y;
            }
        }

        // Try offset positions with ground validation
        Vector3[] offsets = new Vector3[]
        {
            new Vector3(0.5f, 0, 0.5f), new Vector3(-0.5f, 0, 0.5f),
            new Vector3(0.5f, 0, -0.5f), new Vector3(-0.5f, 0, -0.5f),
            new Vector3(1.0f, 0, 0), new Vector3(-1.0f, 0, 0),
            new Vector3(0, 0, 1.0f), new Vector3(0, 0, -1.0f)
        };

        for (int i = 0; i < offsets.Length; i++)
        {
            Vector3 testStart = position + Vector3.up * 2f + offsets[i];
            if (Physics.Raycast(testStart, Vector3.down, out hit, rayLength, geometryLayers.value))
            {
                if (ValidateGroundSolidity(hit.point, hit.normal))
                {
                    return hit.point.y;
                }
            }
        }

        return -1f;
    }

    // NEW: Validate that the ground is actually solid and not thin geometry
    private bool ValidateGroundSolidity(Vector3 groundPoint, Vector3 groundNormal)
    {
        // Check 1: Ensure surface is reasonably horizontal (not a steep wall)
        float upwardness = Vector3.Dot(groundNormal, Vector3.up);
        if (upwardness < minGroundSlopeUpwardness) // Less than ~53 degree slope
        {
            LogInfo($"Ground rejected: too steep (upwardness: {upwardness:F2})");
            return false;
        }

        // Check 2: Verify ground thickness by casting downward from the hit point
        RaycastHit thicknessHit;
        Vector3 thicknessCheckStart = groundPoint + groundNormal * 0.1f; // Start slightly above surface
        float thicknessCheckDistance = groundThicknessCheckDistance; // Check 2m of thickness

        // Cast downward to see if there's solid geometry below
        if (!Physics.Raycast(thicknessCheckStart, Vector3.down, out thicknessHit, thicknessCheckDistance, geometryLayers.value))
        {
            LogInfo($"Ground rejected: insufficient thickness - no solid geometry within {thicknessCheckDistance}m below surface");
            return false;
        }

        // Check 3: Verify we have a reasonable platform size around this point
        float platformRadius = platformSupportRadius; // Player needs at least 1m radius of solid ground
        int validSupportPoints = 0;
        int totalCheckPoints = 8;

        for (int i = 0; i < totalCheckPoints; i++)
        {
            float angle = (i * 360f / totalCheckPoints) * Mathf.Deg2Rad;
            Vector3 checkOffset = new Vector3(Mathf.Cos(angle) * platformRadius, 0, Mathf.Sin(angle) * platformRadius);
            Vector3 checkStart = groundPoint + Vector3.up * 0.5f + checkOffset;

            RaycastHit platformHit;
            if (Physics.Raycast(checkStart, Vector3.down, out platformHit, 1.0f, geometryLayers.value))
            {
                // Check if this support point is at roughly the same height
                float heightDifference = Mathf.Abs(platformHit.point.y - groundPoint.y);
                if (heightDifference < 0.5f) // Within 0.5m height difference
                {
                    validSupportPoints++;
                }
            }
        }

        // Require at least 6 out of 8 support points for a solid platform
        if (validSupportPoints < minPlatformSupportPoints)
        {
            LogInfo($"Ground rejected: insufficient platform support ({validSupportPoints}/{totalCheckPoints} valid points)");
            return false;
        }

        // Check 4: Ensure we're not in a narrow crevasse by checking horizontal clearance
        float clearanceRadius = horizontalClearanceRadius; // Need 0.8m clearance around player
        for (int i = 0; i < 4; i++) // Check 4 cardinal directions
        {
            float angle = i * 90f * Mathf.Deg2Rad;
            Vector3 clearanceDir = new Vector3(Mathf.Cos(angle), 0, Mathf.Sin(angle));
            Vector3 clearanceStart = groundPoint + Vector3.up * 1.0f; // Check at player height

            RaycastHit clearanceHit;
            if (Physics.Raycast(clearanceStart, clearanceDir, out clearanceHit, clearanceRadius, geometryLayers.value))
            {
                LogInfo($"Ground rejected: insufficient clearance in direction {clearanceDir} - hit {clearanceHit.collider.name} at distance {clearanceHit.distance:F2}m");
                return false;
            }
        }

        LogInfo($"Ground validated: solid platform with good support and clearance at Y={groundPoint.y:F2}");
        return true;
    }

    // NEW: Final position validation to prevent immediate falls
    private Vector3 ValidateFinalPosition(Vector3 proposedPosition)
    {
        LogInfo($"=== FINAL POSITION VALIDATION ===");
        LogInfo($"Validating position: {proposedPosition}");

        // Test 1: Simulate a brief physics check to see if player would immediately fall
        Vector3 testPosition = proposedPosition;
        bool wouldFall = SimulateFallCheck(testPosition);

        if (!wouldFall)
        {
            LogInfo("Final position validated - player should be stable");
            return testPosition;
        }

        LogInfo("WARNING: Player would fall from proposed position - searching for safer location");

        // Try positions in expanding rings around the proposed position
        for (float radius = 1f; radius <= 5f; radius += 1f)
        {
            for (int angle = 0; angle < 360; angle += 30)
            {
                float rad = angle * Mathf.Deg2Rad;
                Vector3 testPos = new Vector3(
                    proposedPosition.x + Mathf.Cos(rad) * radius,
                    proposedPosition.y,
                    proposedPosition.z + Mathf.Sin(rad) * radius
                );

                // Find proper ground height at this test position
                float groundY = FindGroundHeight(testPos);
                if (groundY > 0)
                {
                    Vector3 groundedPos = new Vector3(testPos.x, groundY + 0.2f, testPos.z);
                    
                    if (IsPositionSafe(groundedPos) && !SimulateFallCheck(groundedPos))
                    {
                        LogInfo($"Found stable position at radius {radius}m, angle {angle}°: {groundedPos}");
                        return groundedPos;
                    }
                }
            }
        }

        // If all else fails, use a very conservative elevated position at original location
        LogInfo("CRITICAL: Using highly elevated original position to prevent falling");
        Vector3 emergencyPos = new Vector3(initialPosition.x, initialPosition.y + 5f, initialPosition.z);
        return emergencyPos;
    }

    // NEW: Simulate a physics check to predict if player would fall
    private bool SimulateFallCheck(Vector3 testPosition)
    {
        // Create a temporary collision check at the proposed position
        float playerRadius = 0.5f;
        if (characterController != null)
        {
            playerRadius = characterController.radius;
        }

        Vector3 capsuleBottom = testPosition + Vector3.up * (playerRadius + 0.1f);
        Vector3 capsuleTop = testPosition + Vector3.up * 1.8f;

        // Check if player would be supported by ground
        Vector3 groundCheckStart = testPosition + Vector3.up * 0.1f;
        RaycastHit groundHit;

        // Cast downward to find supporting ground
        bool hasGround = Physics.Raycast(groundCheckStart, Vector3.down, out groundHit, fallSimulationGroundCheckDistance, geometryLayers.value);

        if (!hasGround)
        {
            LogInfo("Fall check: No immediate ground support detected");
            return true; // Would fall - no ground support
        }

        // Check if the ground is actually solid and not a thin surface
        if (!ValidateGroundSolidity(groundHit.point, groundHit.normal))
        {
            LogInfo("Fall check: Ground is not solid enough");
            return true; // Would fall - ground not solid
        }

        // Check for adequate horizontal support (not on edge of platform)
        float supportRadius = playerRadius + 0.2f; // Slightly larger than player
        Vector3[] supportCheckDirs = new Vector3[]
        {
            Vector3.forward, Vector3.back, Vector3.left, Vector3.right,
            Vector3.forward + Vector3.left, Vector3.forward + Vector3.right,
            Vector3.back + Vector3.left, Vector3.back + Vector3.right
        };

        int supportingDirections = 0;
        foreach (Vector3 direction in supportCheckDirs)
        {
            Vector3 supportCheckPos = testPosition + direction.normalized * supportRadius;
            Vector3 supportCheckStart = supportCheckPos + Vector3.up * 0.1f;

            RaycastHit supportHit;
            if (Physics.Raycast(supportCheckStart, Vector3.down, out supportHit, fallSimulationGroundCheckDistance, geometryLayers.value))
            {
                // Check if support point is at similar height to main ground
                float heightDiff = Mathf.Abs(supportHit.point.y - groundHit.point.y);
                if (heightDiff < 0.3f) // Within 30cm height difference
                {
                    supportingDirections++;
                }
            }
        }

        // Require support in at least 6 out of 8 directions for stability
        if (supportingDirections < minSupportDirections)
        {
            LogInfo($"Fall check: Insufficient platform support ({supportingDirections}/8 directions)");
            return true; // Would fall - on edge or narrow platform
        }

        LogInfo("Fall check: Position appears stable");
        return false; // Should not fall
    }

    private void CompleteTumble()
    {
        LogInfo("=== COMPLETING TUMBLE ===");
        
        if (playerController != null && ragdollObject != null)
        {
            Vector3 finalPosition = ragdollObject.transform.position;
            LogInfo($"Ragdoll final position: {finalPosition}");

            // Use robust safe positioning 
            finalPosition = EnsureSafePosition(finalPosition);
            LogInfo($"Final safe position: {finalPosition}");

            // Double-check the final position is actually safe
            if (!IsPositionSafe(finalPosition))
            {
                LogInfo("WARNING: Final position still not safe - using emergency fallback");
                finalPosition = GetFallbackSafePosition(finalPosition);
            }

            // NEW: Final safety validation - ensure player won't immediately fall
            finalPosition = ValidateFinalPosition(finalPosition);

            // Position player with proper rotation
            Quaternion finalRotation = Quaternion.Euler(0f, ragdollObject.transform.eulerAngles.y, 0f);
            
            if (playerMotor != null)
            {
                playerMotor.SetPositionAndRotation(finalPosition, finalRotation);
                LogInfo("Positioned player using character motor");
            }
            else
            {
                playerController.transform.position = finalPosition;
                playerController.transform.rotation = finalRotation;
                LogInfo("Positioned player using transform directly");
            }

            // Final validation - ensure we have ground support
            float finalGroundCheck = FindGroundHeight(finalPosition);
            if (finalGroundCheck <= 0)
            {
                LogInfo("CRITICAL WARNING: Final position has no ground support!");
            }
            else
            {
                float groundDistance = finalPosition.y - finalGroundCheck;
                LogInfo($"Final position is {groundDistance:F2}m above ground at Y={finalGroundCheck:F2}");
                
                if (groundDistance > 2f)
                {
                    LogInfo("WARNING: Player positioned unusually high above ground - possible fall incoming");
                }
            }
        }

        // Clean up ragdoll
        if (ragdollObject != null)
        {
            if (ragdollRigidbodies != null && ragdollRigidbodies.Length > 0)
            {
                for (int i = 0; i < ragdollRigidbodies.Length; i++)
                {
                    if (i < ragdollRigidbodies.Length && ragdollRigidbodies[i] != null)
                    {
                        ragdollRigidbodies[i].isKinematic = false;
                    }
                }
            }
            else if (ragdollRigidbody != null)
            {
                ragdollRigidbody.isKinematic = false;
            }

            ragdollObject.SetActive(false);
        }

        SwitchCameras(false);
        EnablePlayerControl();

        isTumbling = false;
        isRecovering = false;
        tumbleCooldownTimer = TUMBLE_COOLDOWN;

        if (generateDebugReport)
        {
            Debug.Log(GetDebugReport());
        }
        
        LogInfo("=== TUMBLE COMPLETION SUCCESSFUL ===");
    }

    private Vector3 EnsureSafePosition(Vector3 proposedPosition)
    {
        LogInfo($"=== ROBUST SAFE POSITION CHECK ===");
        LogInfo($"Proposed position: {proposedPosition}");
        LogInfo($"Initial position: {initialPosition}");
        
        // Step 1: Verify ground exists at proposed position
        float groundY = FindGroundHeight(proposedPosition);
        if (groundY <= 0)
        {
            LogInfo("CRITICAL: No ground found at proposed position - using fallback");
            return GetFallbackSafePosition(proposedPosition);
        }
        
        // Step 2: Ensure we're positioned above ground properly
        Vector3 groundedPosition = new Vector3(proposedPosition.x, groundY + 0.2f, proposedPosition.z);
        LogInfo($"Grounded position: {groundedPosition}");
        
        // Step 3: Check for overlaps with more robust detection
        if (IsPositionSafe(groundedPosition))
        {
            LogInfo("Grounded position is safe");
            return groundedPosition;
        }
        
        LogInfo("Grounded position has overlaps - trying adjustments");
        
        // Step 4: Try moving up incrementally 
        for (int i = 1; i <= maxRepositionAttempts * 2; i++) // Try more attempts
        {
            Vector3 adjustedPosition = new Vector3(proposedPosition.x, groundY + (0.2f + i * 0.3f), proposedPosition.z);
            
            if (IsPositionSafe(adjustedPosition))
            {
                LogInfo($"Found safe position by moving up {i * 0.3f}m");
                return adjustedPosition;
            }
        }
        
        // Step 5: Try offset positions around the area
        Vector3[] horizontalOffsets = new Vector3[]
        {
            new Vector3(1f, 0, 0), new Vector3(-1f, 0, 0),
            new Vector3(0, 0, 1f), new Vector3(0, 0, -1f),
            new Vector3(1f, 0, 1f), new Vector3(-1f, 0, 1f),
            new Vector3(1f, 0, -1f), new Vector3(-1f, 0, -1f),
            new Vector3(2f, 0, 0), new Vector3(-2f, 0, 0),
            new Vector3(0, 0, 2f), new Vector3(0, 0, -2f)
        };
        
        foreach (Vector3 offset in horizontalOffsets)
        {
            Vector3 offsetPosition = proposedPosition + offset;
            float offsetGroundY = FindGroundHeight(offsetPosition);
            
            if (offsetGroundY > 0)
            {
                Vector3 testPosition = new Vector3(offsetPosition.x, offsetGroundY + 0.2f, offsetPosition.z);
                
                if (IsPositionSafe(testPosition))
                {
                    LogInfo($"Found safe position with offset {offset}: {testPosition}");
                return testPosition;
            }
            }
        }
        
        // Step 6: Emergency fallback
        LogInfo("All positioning attempts failed - using emergency fallback");
        return GetFallbackSafePosition(proposedPosition);
    }
    
    private bool IsPositionSafe(Vector3 position)
    {
        float checkRadius = overlapCheckRadius;
        if (characterController != null)
        {
            checkRadius = characterController.radius * 0.9f; // Slightly more conservative
        }

        // Check player-sized capsule for overlaps
        Vector3 capsuleBottom = position + Vector3.up * (checkRadius + 0.1f);
        Vector3 capsuleTop = position + Vector3.up * 1.8f;
        
        Collider[] overlaps = Physics.OverlapCapsule(capsuleBottom, capsuleTop, checkRadius, geometryLayers);
        
        if (overlaps.Length == 0)
        {
            return true; // No overlaps = safe
        }
        
        // Filter overlaps to check if they're actually problematic
        int problematicOverlaps = 0;
        
        foreach (Collider overlap in overlaps)
        {
            if (overlap == null) continue;
            
            Vector3 closestPoint = overlap.ClosestPoint(position);
            float verticalDistance = closestPoint.y - position.y;
            
            // If collision is mostly below us (ground contact), it's probably okay
            if (verticalDistance <= 0.1f && verticalDistance >= -0.5f)
            {
                Vector3 surfaceNormal = (position - closestPoint).normalized;
                float upwardness = Vector3.Dot(surfaceNormal, Vector3.up);
                
                if (upwardness > 0.6f) // Clearly ground-like surface
                {
                    continue; // Skip this overlap - it's legitimate ground
                }
            }
            
            // This is a problematic overlap (wall, ceiling, etc.)
            problematicOverlaps++;
            LogInfo($"Problematic overlap with {overlap.name} at {closestPoint}");
            
            if (problematicOverlaps >= 1) // Be strict - any wall collision is bad
            {
                break;
            }
        }
        
        return problematicOverlaps == 0;
    }
    
    private Vector3 GetFallbackSafePosition(Vector3 proposedPosition)
    {
        LogInfo("=== EMERGENCY FALLBACK POSITIONING ===");
        
        // Try original position first
        float originalGroundY = FindGroundHeight(initialPosition);
        if (originalGroundY > 0)
        {
            Vector3 originalSafe = new Vector3(initialPosition.x, originalGroundY + 0.5f, initialPosition.z);
            if (IsPositionSafe(originalSafe))
            {
                LogInfo("Using original position as fallback");
                return originalSafe;
            }
        }
        
        // Try positions in a wider radius around original position
        for (float radius = 3f; radius <= 10f; radius += 2f)
        {
            for (int angle = 0; angle < 360; angle += 45)
            {
                float rad = angle * Mathf.Deg2Rad;
                Vector3 testPos = initialPosition + new Vector3(
                    Mathf.Cos(rad) * radius,
                    0,
                    Mathf.Sin(rad) * radius
                );
                
                float testGroundY = FindGroundHeight(testPos);
                if (testGroundY > 0)
                {
                    Vector3 emergencyPosition = new Vector3(testPos.x, testGroundY + 0.5f, testPos.z);
                    if (IsPositionSafe(emergencyPosition))
                    {
                        LogInfo($"Emergency position found at radius {radius}m, angle {angle}°");
                        return emergencyPosition;
                    }
                }
            }
        }
        
        // Final desperate fallback - use original position elevated
        LogInfo("CRITICAL: Using elevated original position as last resort");
        return new Vector3(initialPosition.x, initialPosition.y + 3f, initialPosition.z);
    }

    private void ForceEndTumble()
    {
        if (ragdollObject != null)
        {
            ragdollObject.SetActive(false);
        }

        EnablePlayerControl();

        if (playerCamera != null && ragdollCamera != null)
        {
            ragdollCamera.enabled = false;
            playerCamera.enabled = true;
            isCameraTransitioning = false;
        }

        if (playerModel != null)
        {
            playerModel.SetActive(true);
        }

        isTumbling = false;
        isRecovering = false;
        tumbleTimer = 0f;
        recoveryTimer = 0f;

        LogInfo("Force ended tumble due to player death");
    }

    public void ResetRagdollState()
    {
        if (isTumbling)
        {
            if (ragdollObject != null)
            {
                ragdollObject.SetActive(false);
            }

            EnablePlayerControl();

            if (playerCamera != null)
            {
                playerCamera.transform.position = originalCameraPosition;
                playerCamera.transform.rotation = originalCameraRotation;
                
                ragdollCamera.enabled = false;
                playerCamera.enabled = true;
                isCameraTransitioning = false;
                cameraTransitionProgress = 0f;
            }

            if (playerModel != null)
            {
                playerModel.SetActive(true);
            }

            isTumbling = false;
            isRecovering = false;
            tumbleTimer = 0f;
            recoveryTimer = 0f;
        }

        EnablePlayerControl();
        LogInfo("Ragdoll state reset completed");
    }

    public void OnPlayerRespawn()
    {
        LogInfo("Player respawned - resetting ragdoll state");
        ResetRagdollState();
    }

    public void LogCollision(string colliderName, Collision collision, bool isEnter)
    {
        if (!debugCollisions || collision == null) return;

        try
        {
            string otherObject = collision.gameObject.name;
            string otherLayer = LayerMask.LayerToName(collision.gameObject.layer);

            if (isEnter)
            {
                collisionCount++;
                LogInfo($"[COLLISION #{collisionCount}] {colliderName} hit {otherObject} (layer: {otherLayer})");
            }
        }
        catch (System.Exception e)
        {
            LogError($"Error in collision logging: {e.Message}");
        }
    }

    public string GetDebugReport()
    {
        return debugReportBuilder.ToString();
    }

    public void TestTumble(float velocity)
    {
        StartTumble(velocity, new Vector3(UnityEngine.Random.Range(-1f, 1f), 0, UnityEngine.Random.Range(-1f, 1f)));
    }

    public void TestTumbleFromInspector()
    {
        TestTumble(8f);
    }

    private void LogInfo(string message)
    {
        if (debugLog)
        {
            Debug.Log($"[RagdollTumble] {message}");
        }
    }

    private void LogWarning(string message)
    {
        if (debugLog)
        {
            Debug.LogWarning($"[RagdollTumble] {message}");
        }
    }

    private void LogError(string message)
{
    Debug.LogError($"[RagdollTumble] {message}");
}

// NEW: Method to resolve any penetrations with geometry
private void ResolvePenetrations()
{
    if (ragdollRigidbodies == null) return;

    foreach (Rigidbody rb in ragdollRigidbodies)
    {
        Collider rbCol = rb.GetComponent<Collider>();
        if (rbCol == null) continue;

        Collider[] overlaps = Physics.OverlapBox(rbCol.bounds.center, rbCol.bounds.extents, rbCol.transform.rotation, geometryLayers);

        foreach (Collider other in overlaps)
        {
            if (other == rbCol || other.gameObject.layer == ragdollLayer) continue;

            Vector3 direction;
            float distance;

            if (Physics.ComputePenetration(rbCol, rb.transform.position, rb.transform.rotation,
                other, other.transform.position, other.transform.rotation,
                out direction, out distance))
            {
                rb.position += direction * (distance + 0.01f);
                LogInfo($"Resolved penetration for {rb.name} by {distance:F2}m");
            }
        }
    }
}

}

public class CollisionDebugger : MonoBehaviour
{
    public RagdollTumbleSystem tumbleSystem;

    private void OnCollisionEnter(Collision collision)
    {
        if (tumbleSystem != null)
        {
            tumbleSystem.LogCollision(gameObject.name, collision, true);
        }
    }

    private void OnCollisionExit(Collision collision)
    {
        if (tumbleSystem != null)
        {
            tumbleSystem.LogCollision(gameObject.name, collision, false);
        }
    }
}

