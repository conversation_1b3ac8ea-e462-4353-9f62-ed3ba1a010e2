{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754352107280643, "dur": 8912, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754352107289561, "dur": 158537, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754352107448110, "dur": 4406, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 54481442, "ts": 1754352109800869, "dur": 1708, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107277863, "dur": 107037, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107384904, "dur": 2399180, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107386044, "dur": 2358, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107388409, "dur": 1426, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107389839, "dur": 54, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107389896, "dur": 535, "ph": "X", "name": "ProcessMessages 467", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107390434, "dur": 2664, "ph": "X", "name": "ReadAsync 467", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107393103, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107393106, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107393134, "dur": 274, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107393411, "dur": 6874, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107400290, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107400294, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107400320, "dur": 597, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352107400920, "dur": 2363884, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109764812, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109764816, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109764857, "dur": 3196, "ph": "X", "name": "ProcessMessages 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109768058, "dur": 4425, "ph": "X", "name": "ReadAsync 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109772490, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109772492, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109772522, "dur": 416, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109772943, "dur": 211, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109773157, "dur": 405, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754352109773566, "dur": 9806, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 54481442, "ts": 1754352109802582, "dur": 52, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 8589934592, "ts": 1754352107274237, "dur": 178326, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754352107452566, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754352107452572, "dur": 1350, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 54481442, "ts": 1754352109802637, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 4294967296, "ts": 1754352107231638, "dur": 2553602, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754352107236934, "dur": 20163, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754352109785314, "dur": 5961, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754352109788315, "dur": 60, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754352109791821, "dur": 21, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 54481442, "ts": 1754352109802646, "dur": 103, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754352107383195, "dur":4220, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352107387428, "dur":192, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352107387682, "dur":1451, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352107389174, "dur":95, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352107389270, "dur":2383609, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352109772880, "dur":206, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352109773300, "dur":3948, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754352107389832, "dur":945, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352107389424, "dur":10146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1754352107401627, "dur":2364700, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1754352107410135, "dur":2362727, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352107410286, "dur":2362571, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352107394798, "dur":2378060, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352107393444, "dur":60844, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352107463972, "dur":475, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":5, "ts":1754352107454289, "dur":10163, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352107464453, "dur":2308412, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352107394770, "dur":69722, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352107464493, "dur":2308368, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352107394891, "dur":2377972, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352107410325, "dur":2362555, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352107410393, "dur":2362489, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352107394814, "dur":2378061, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352107394852, "dur":2378007, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352107394917, "dur":2377938, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352109782769, "dur":441, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 54481442, "ts": 1754352109803699, "dur": 15535, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 54481442, "ts": 1754352109819415, "dur": 2490, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 54481442, "ts": 1754352109799866, "dur": 23078, "ph": "X", "name": "Write chrome-trace events", "args": {} },
