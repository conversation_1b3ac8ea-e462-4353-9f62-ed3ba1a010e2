{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754349952735816, "dur": 2690, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754349952738511, "dur": 34419, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754349952772933, "dur": 1329, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 53599388, "ts": 1754349952804716, "dur": 11, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952735783, "dur": 41442, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952777226, "dur": 26536, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952777233, "dur": 22, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952777260, "dur": 585, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952777850, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952777853, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952777882, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952777887, "dur": 3909, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952781800, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952781803, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952781852, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952781855, "dur": 206, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952782066, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952782096, "dur": 6077, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952788178, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952788182, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952788203, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952788205, "dur": 4826, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952793035, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952793038, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952793063, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754349952793065, "dur": 10690, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 53599388, "ts": 1754349952804729, "dur": 36, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 47244640256, "ts": 1754349952735744, "dur": 38540, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ts": 1754349952774285, "dur": 2886, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ts": 1754349952777172, "dur": 46, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 53599388, "ts": 1754349952804767, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 42949672960, "ts": 1754349952724050, "dur": 79748, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754349952724182, "dur": 11526, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754349952803801, "dur": 7, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754349952803810, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 53599388, "ts": 1754349952804776, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754349952777141, "dur":3575, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349952780729, "dur":213, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349952780981, "dur":644, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349952781698, "dur":73, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349952781771, "dur":10857, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349952792629, "dur":178, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349952792960, "dur":4083, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754349952782178, "dur":10469, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349952782000, "dur":286, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349952792263, "dur":322, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1754349952782287, "dur":10302, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349952781977, "dur":6028, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":3, "ts":1754349952788007, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349952788098, "dur":4515, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349952782002, "dur":10617, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349952782010, "dur":10602, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349952782206, "dur":10436, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349952782230, "dur":10405, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349952782102, "dur":10509, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349952782142, "dur":10467, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349952782035, "dur":10572, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349952782057, "dur":10551, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349952782254, "dur":10372, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349952802674, "dur":414, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 53599388, "ts": 1754349952804809, "dur": 8751, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 53599388, "ts": 1754349952813687, "dur": 593, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 53599388, "ts": 1754349952804712, "dur": 9604, "ph": "X", "name": "Write chrome-trace events", "args": {} },
