using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;
using System.Collections.Generic;

public class NotificationManager : MonoBehaviour
{
    [System.Serializable]
    public class NotificationSettings
    {
        public float displayTime = 3f;
        public int maxNotifications = 5;
        public float animationDuration = 0.3f;
    }


    [SerializeField] private UIDocument uiDocument;
    [SerializeField] private NotificationSettings settings;
    [SerializeField] private VisualTreeAsset notificationTemplate;

    private VisualElement root;
    private VisualElement mainContainer;
    private List<VisualElement> activeNotifications = new List<VisualElement>();

    public static NotificationManager Instance { get; private set; }

    private void Awake()
    {
        // Just set the instance without any checks
        Instance = this;
    }

    private void OnEnable()
    {
        if (uiDocument == null || uiDocument.rootVisualElement == null)
        {
            Debug.LogError("UIDocument is not properly set up.");
            return;
        }

        root = uiDocument.rootVisualElement;
        mainContainer = root.Q<VisualElement>("AlertMainContainer");

        if (mainContainer == null)
        {
            Debug.LogError("AlertMainContainer not found in the UXML.");
        }
    }

    public void ShowNotification(string message)
    {
        if (mainContainer == null || notificationTemplate == null)
        {
            Debug.LogError("mainContainer or notificationTemplate is null");
            return;
        }

        VisualElement notification = notificationTemplate.Instantiate();
        notification.Q<Label>("Text").text = message;

        notification.AddToClassList("notification-enter");
        mainContainer.Add(notification);
        activeNotifications.Add(notification);

        if (activeNotifications.Count > settings.maxNotifications)
        {
            RemoveOldestNotification();
        }

        StartCoroutine(AnimateNotification(notification));
    }

    private IEnumerator AnimateNotification(VisualElement notification)
    {
        yield return new WaitForEndOfFrame();
        notification.RemoveFromClassList("notification-enter");
        notification.AddToClassList("notification-enter-active");

        yield return new WaitForSeconds(settings.displayTime);

        notification.RemoveFromClassList("notification-enter-active");
        notification.AddToClassList("notification-exit-active");

        yield return new WaitForSeconds(settings.animationDuration);

        if (notification.parent == mainContainer)
        {
            mainContainer.Remove(notification);
        }
        activeNotifications.Remove(notification);
    }

    private void RemoveOldestNotification()
    {
        if (activeNotifications.Count > 0 && mainContainer != null)
        {
            VisualElement oldestNotification = activeNotifications[0];
            mainContainer.Remove(oldestNotification);
            activeNotifications.RemoveAt(0);
        }
    }
}