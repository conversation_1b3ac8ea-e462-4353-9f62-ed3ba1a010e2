using UnityEngine;
using UnityEngine.SceneManagement;
using KinematicCharacterController;
using KinematicCharacterController.FPS;
using System.Collections;

public class FPSPlayerManager : MonoBehaviour
{
    [Header("References")]
    public FPSCharacterController Character;
    public Transform CameraTransform;
    public GameObject playerObject;
    public float MouseSensitivity = 2f;
    public bool InvertY = false;

    private const string MouseXInput = "Mouse X";
    private const string MouseYInput = "Mouse Y";
    private const string HorizontalInput = "Horizontal";
    private const string VerticalInput = "Vertical";
    
    // Reference to inventory UI to check if it's open
    private InvUI inventoryUI;
    
    // Reference to grab interaction 
    private GrabInteraction grabInteraction;

    private bool wasInventoryOpenLastFrame = false;

    private void OnEnable()
    {
        SceneManager.sceneLoaded += OnSceneLoaded;
    }
    private void OnDisable()
    {
        SceneManager.sceneLoaded -= OnSceneLoaded;
    }

    private void Awake()
    {
        // Find the inventory UI
        inventoryUI = FindObjectOfType<InvUI>();
        
        // Find grab interaction
        grabInteraction = FindObjectOfType<GrabInteraction>();
        if (grabInteraction == null)
        {
            Debug.LogWarning("[FPSPlayerManager] GrabInteraction component not found");
        }
    }

    private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
    {
        // Use PlayerProgressionManager as single source of truth for player position
        if (PlayerProgressionManager.Instance != null &&
            PlayerProgressionManager.Instance.TryGetSavedPlayerTransform(out Vector3 savedPos, out Quaternion savedRot))
        {
            StartCoroutine(SetPositionNextFrame(savedPos, savedRot));
        }
    }

    private IEnumerator SetPositionNextFrame(Vector3 position, Quaternion rotation)
    {
        yield return null;

        while (Character == null || Character.Motor == null || CameraTransform == null ||
               Character.CameraFollowPoint == null)
        {
            yield return null;
        }

        if (Character != null)
        {
            Character.Motor.SetPositionAndRotation(position, rotation, true);
        }

        if (CameraTransform != null)
        {
            var cameraController = CameraTransform.GetComponent<FPSCharacterCamera>();
            if (cameraController != null)
            {
                // Try to get saved camera rotation, fallback to player rotation if not available
                if (PlayerProgressionManager.Instance.TryGetSavedCameraRotation(out float pitch, out float yaw))
                {
                    cameraController.SetInitialRotation(pitch, yaw);
                }
                else
                {
                    // Fallback to player rotation
                    cameraController.SetInitialRotation(rotation.eulerAngles.x, rotation.eulerAngles.y);
                }
            }
        }
    }

    private void Start()
    {
        // Lock the cursor on start if inventory is not open
        if (inventoryUI == null || !inventoryUI.IsInventoryVisible())
        {
            Cursor.lockState = CursorLockMode.Locked;
        }
        
        // Optionally, call OnSceneLoaded manually for the active scene:
        OnSceneLoaded(SceneManager.GetActiveScene(), LoadSceneMode.Single);
    }

    private void Update()
    {
        // Check if grab rotation is active - camera will be disabled during this time
        // The grab system will handle the cursor and mouse input directly
        if (IsGrabRotationActive())
        {
            // We still want to handle movement with WASD, just not camera or other inputs
            HandleCharacterInput(true);
            return;
        }
        
        bool isInventoryOpen = inventoryUI != null && inventoryUI.IsInventoryVisible();
        
        // Only handle cursor locking if inventory is not open
        if (isInventoryOpen)
        {
            // Don't handle camera input or lock cursor when inventory is open
            // But DO still handle character movement input
            HandleCharacterInput(true);
            return;
        }
        
        if (Input.GetMouseButtonDown(0))
        {
            if (Cursor.lockState != CursorLockMode.Locked)
            {
                Cursor.lockState = CursorLockMode.Locked;
            }
        }
        HandleCharacterInput(false);
    }

    private void LateUpdate()
    {
        // Skip if grab rotation is active
        if (IsGrabRotationActive())
            return;
            
        // Handle cursor state when inventory is closed
        bool isInventoryOpenNow = inventoryUI != null && inventoryUI.IsInventoryVisible();
        
        // Check if inventory was just closed this frame
        if (wasInventoryOpenLastFrame && !isInventoryOpenNow)
        {
            // Inventory was just closed - force cursor to be locked
            Cursor.visible = false;
            Cursor.lockState = CursorLockMode.Locked;
        }
        
        wasInventoryOpenLastFrame = isInventoryOpenNow;
    }
    
    private bool IsGrabRotationActive()
    {
        // Check if player is in grab rotation mode (right-clicking while holding an object)
        return grabInteraction != null && 
               grabInteraction.IsGrabbing && 
               Input.GetMouseButton(1);
    }

    private void HandleCharacterInput(bool restrictedMode = false)
    {
        // Check if either C or Control is pressed/released for crouch
        bool crouchKeyDown = Input.GetKeyDown(KeyCode.C) || Input.GetKeyDown(KeyCode.LeftControl);
        bool crouchKeyUp = Input.GetKeyUp(KeyCode.C) || Input.GetKeyUp(KeyCode.LeftControl);
        
        PlayerCharacterInputs characterInputs = new PlayerCharacterInputs
        {
            MoveAxisForward = Input.GetAxisRaw(VerticalInput),
            MoveAxisRight = Input.GetAxisRaw(HorizontalInput),
            CameraRotation = CameraTransform ? CameraTransform.rotation : Quaternion.identity,
            JumpDown = Input.GetKeyDown(KeyCode.Space) && !restrictedMode,
            CrouchDown = crouchKeyDown && !restrictedMode,
            CrouchUp = crouchKeyUp && !restrictedMode,
            SprintDown = Input.GetKeyDown(KeyCode.LeftShift) && !restrictedMode,
            SprintUp = Input.GetKeyUp(KeyCode.LeftShift) && !restrictedMode
        };
        
        // Update vehicle-specific inputs
        UpdatePlayerCharacterInputs(ref characterInputs);
        
        Character.SetInputs(ref characterInputs);
    }

    public void UpdatePlayerCharacterInputs(ref PlayerCharacterInputs inputs)
    {
        // Vehicle-specific inputs (only when in vehicle)
        if (Character.CurrentCharacterState == CharacterState.Vehicle)
        {
            // Movement - WASD for front/left/back/right
            inputs.VehicleThrottleAxis = Input.GetAxis("Vertical"); // W/S for forward/backward
            inputs.MoveAxisRight = Input.GetAxis("Horizontal"); // A/D for left/right strafe
            
            // Vertical movement - Space/Ctrl for up/down (proper spaceship controls)
            inputs.VehicleVerticalAxis = 0f;
            if (Input.GetKey(KeyCode.Space)) inputs.VehicleVerticalAxis = 1f;
            if (Input.GetKey(KeyCode.LeftControl)) inputs.VehicleVerticalAxis = -1f;
            
            // Roll - Q/R for rotation
            inputs.VehicleRollAxis = 0f;
            if (Input.GetKey(KeyCode.Q)) inputs.VehicleRollAxis = -1f;
            if (Input.GetKey(KeyCode.R)) inputs.VehicleRollAxis = 1f;
            
            // Pitch/Yaw - Mouse for pitch and yaw (standard flight controls)
            inputs.VehicleLookInput = new Vector2(
                Input.GetAxis("Mouse X"), // Mouse X for yaw
                Input.GetAxis("Mouse Y")  // Mouse Y for pitch
            );
            
            // Boost
            inputs.VehicleBoostDown = Input.GetKeyDown(KeyCode.LeftShift);
            inputs.VehicleBoostUp = Input.GetKeyUp(KeyCode.LeftShift);
            
            // Exit
            inputs.VehicleExitRequested = Input.GetKeyDown(KeyCode.F);
            
            // Set vehicle state flag
            inputs.IsInVehicle = true;
        }
        else
        {
            // Clear vehicle inputs when not in vehicle
            inputs.VehicleThrottleAxis = 0f;
            inputs.VehicleVerticalAxis = 0f;
            inputs.VehicleRollAxis = 0f;
            inputs.VehicleLookInput = Vector2.zero;
            inputs.VehicleBoostDown = false;
            inputs.VehicleBoostUp = false;
            inputs.VehicleExitRequested = false;
            inputs.IsInVehicle = false;
        }
    }
}
