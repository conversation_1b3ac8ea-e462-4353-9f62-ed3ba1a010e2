{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754352110854485, "dur": 9373, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754352110863865, "dur": 998195, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754352111862062, "dur": 27274, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 54485999, "ts": 1754352116647258, "dur": 11, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110853941, "dur": 40748, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110894690, "dur": 5751459, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110894699, "dur": 37, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110894739, "dur": 1061, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110895807, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110895868, "dur": 469, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110896341, "dur": 4639, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110900987, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110900990, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901116, "dur": 3, "ph": "X", "name": "ProcessMessages 2313", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901120, "dur": 27, "ph": "X", "name": "ReadAsync 2313", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901152, "dur": 35, "ph": "X", "name": "ReadAsync 93", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901190, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901192, "dur": 37, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901233, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901235, "dur": 35, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901272, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901274, "dur": 103, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901381, "dur": 2, "ph": "X", "name": "ProcessMessages 1216", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901384, "dur": 92, "ph": "X", "name": "ReadAsync 1216", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901479, "dur": 59, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901540, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901587, "dur": 2, "ph": "X", "name": "ProcessMessages 1443", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901589, "dur": 52, "ph": "X", "name": "ReadAsync 1443", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901695, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901697, "dur": 42, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901743, "dur": 2, "ph": "X", "name": "ProcessMessages 1416", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901745, "dur": 25, "ph": "X", "name": "ReadAsync 1416", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901774, "dur": 33, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901809, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110901811, "dur": 692, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110902853, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110902856, "dur": 142, "ph": "X", "name": "ReadAsync 493", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903001, "dur": 8, "ph": "X", "name": "ProcessMessages 10613", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903010, "dur": 34, "ph": "X", "name": "ReadAsync 10613", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903048, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903050, "dur": 37, "ph": "X", "name": "ReadAsync 612", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903092, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903207, "dur": 1, "ph": "X", "name": "ProcessMessages 1404", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903210, "dur": 34, "ph": "X", "name": "ReadAsync 1404", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903247, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903250, "dur": 41, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903293, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903295, "dur": 105, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903403, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903405, "dur": 116, "ph": "X", "name": "ReadAsync 547", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903524, "dur": 2, "ph": "X", "name": "ProcessMessages 1622", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903527, "dur": 89, "ph": "X", "name": "ReadAsync 1622", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903620, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903661, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903663, "dur": 36, "ph": "X", "name": "ReadAsync 489", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903770, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903773, "dur": 54, "ph": "X", "name": "ReadAsync 223", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903829, "dur": 2, "ph": "X", "name": "ProcessMessages 844", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903832, "dur": 102, "ph": "X", "name": "ReadAsync 844", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903936, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110903938, "dur": 34, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904033, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904036, "dur": 335, "ph": "X", "name": "ReadAsync 660", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904373, "dur": 2, "ph": "X", "name": "ProcessMessages 1601", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904376, "dur": 50, "ph": "X", "name": "ReadAsync 1601", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904429, "dur": 2, "ph": "X", "name": "ProcessMessages 1311", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904432, "dur": 86, "ph": "X", "name": "ReadAsync 1311", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904520, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904522, "dur": 39, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904563, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904565, "dur": 28, "ph": "X", "name": "ReadAsync 953", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904596, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904598, "dur": 103, "ph": "X", "name": "ReadAsync 269", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904703, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904706, "dur": 103, "ph": "X", "name": "ReadAsync 871", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904813, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904903, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110904905, "dur": 109, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905016, "dur": 2, "ph": "X", "name": "ProcessMessages 1653", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905019, "dur": 36, "ph": "X", "name": "ReadAsync 1653", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905057, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905059, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905082, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905084, "dur": 25, "ph": "X", "name": "ReadAsync 465", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905112, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905114, "dur": 27, "ph": "X", "name": "ReadAsync 268", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905143, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905145, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905170, "dur": 19, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905257, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905259, "dur": 218, "ph": "X", "name": "ReadAsync 83", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905479, "dur": 1, "ph": "X", "name": "ProcessMessages 1046", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905482, "dur": 47, "ph": "X", "name": "ReadAsync 1046", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905532, "dur": 2, "ph": "X", "name": "ProcessMessages 1919", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905536, "dur": 261, "ph": "X", "name": "ReadAsync 1919", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905834, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110905836, "dur": 352, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906191, "dur": 3, "ph": "X", "name": "ProcessMessages 3817", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906196, "dur": 54, "ph": "X", "name": "ReadAsync 3817", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906251, "dur": 2, "ph": "X", "name": "ProcessMessages 1708", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906263, "dur": 71, "ph": "X", "name": "ReadAsync 1708", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906345, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906365, "dur": 41, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906409, "dur": 165, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906576, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906578, "dur": 31, "ph": "X", "name": "ReadAsync 833", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906612, "dur": 1, "ph": "X", "name": "ProcessMessages 1165", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906615, "dur": 19, "ph": "X", "name": "ReadAsync 1165", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906637, "dur": 24, "ph": "X", "name": "ReadAsync 229", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906665, "dur": 38, "ph": "X", "name": "ReadAsync 136", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906706, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906708, "dur": 101, "ph": "X", "name": "ReadAsync 540", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906811, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906813, "dur": 24, "ph": "X", "name": "ReadAsync 547", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906840, "dur": 56, "ph": "X", "name": "ReadAsync 377", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906951, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110906953, "dur": 107, "ph": "X", "name": "ReadAsync 505", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907062, "dur": 2, "ph": "X", "name": "ProcessMessages 1556", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907065, "dur": 24, "ph": "X", "name": "ReadAsync 1556", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907093, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907118, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907120, "dur": 30, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907219, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907304, "dur": 1, "ph": "X", "name": "ProcessMessages 1218", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907421, "dur": 87, "ph": "X", "name": "ReadAsync 1218", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907510, "dur": 2, "ph": "X", "name": "ProcessMessages 2224", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907570, "dur": 26, "ph": "X", "name": "ReadAsync 2224", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907598, "dur": 1, "ph": "X", "name": "ProcessMessages 1116", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907600, "dur": 12, "ph": "X", "name": "ReadAsync 1116", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907615, "dur": 10, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907627, "dur": 149, "ph": "X", "name": "ReadAsync 125", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907779, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907781, "dur": 9, "ph": "X", "name": "ReadAsync 783", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907792, "dur": 20, "ph": "X", "name": "ReadAsync 190", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110907814, "dur": 802, "ph": "X", "name": "ReadAsync 319", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110908619, "dur": 4, "ph": "X", "name": "ProcessMessages 3752", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110908624, "dur": 32, "ph": "X", "name": "ReadAsync 3752", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110908660, "dur": 48, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110908711, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110908713, "dur": 205, "ph": "X", "name": "ReadAsync 558", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110908994, "dur": 2, "ph": "X", "name": "ProcessMessages 1139", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110908997, "dur": 209, "ph": "X", "name": "ReadAsync 1139", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909209, "dur": 4, "ph": "X", "name": "ProcessMessages 3953", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909215, "dur": 99, "ph": "X", "name": "ReadAsync 3953", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909451, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909453, "dur": 57, "ph": "X", "name": "ReadAsync 375", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909513, "dur": 3, "ph": "X", "name": "ProcessMessages 2200", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909517, "dur": 27, "ph": "X", "name": "ReadAsync 2200", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909548, "dur": 24, "ph": "X", "name": "ReadAsync 218", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909575, "dur": 30, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909608, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909610, "dur": 29, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909642, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909705, "dur": 37, "ph": "X", "name": "ReadAsync 494", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909744, "dur": 1, "ph": "X", "name": "ProcessMessages 1149", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909746, "dur": 92, "ph": "X", "name": "ReadAsync 1149", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909906, "dur": 2, "ph": "X", "name": "ProcessMessages 1283", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110909909, "dur": 106, "ph": "X", "name": "ReadAsync 1283", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910017, "dur": 2, "ph": "X", "name": "ProcessMessages 2573", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910020, "dur": 28, "ph": "X", "name": "ReadAsync 2573", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910050, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910052, "dur": 30, "ph": "X", "name": "ReadAsync 463", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910084, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910086, "dur": 98, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910187, "dur": 1, "ph": "X", "name": "ProcessMessages 994", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910190, "dur": 40, "ph": "X", "name": "ReadAsync 994", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910233, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910266, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910268, "dur": 25, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910295, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910297, "dur": 99, "ph": "X", "name": "ReadAsync 668", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910398, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910400, "dur": 28, "ph": "X", "name": "ReadAsync 947", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910431, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910433, "dur": 531, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910967, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110910969, "dur": 92, "ph": "X", "name": "ReadAsync 587", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911064, "dur": 6, "ph": "X", "name": "ProcessMessages 6920", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911071, "dur": 27, "ph": "X", "name": "ReadAsync 6920", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911099, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911101, "dur": 22, "ph": "X", "name": "ReadAsync 563", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911127, "dur": 16, "ph": "X", "name": "ReadAsync 11", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911146, "dur": 11, "ph": "X", "name": "ReadAsync 184", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911160, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911188, "dur": 205, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911396, "dur": 2, "ph": "X", "name": "ProcessMessages 2093", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911399, "dur": 20, "ph": "X", "name": "ReadAsync 2093", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911422, "dur": 9, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911433, "dur": 9, "ph": "X", "name": "ReadAsync 65", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911444, "dur": 7, "ph": "X", "name": "ReadAsync 228", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911453, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911465, "dur": 10, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911478, "dur": 32, "ph": "X", "name": "ReadAsync 58", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911515, "dur": 182, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911700, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911702, "dur": 53, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911758, "dur": 3, "ph": "X", "name": "ProcessMessages 2723", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911762, "dur": 158, "ph": "X", "name": "ReadAsync 2723", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911984, "dur": 2, "ph": "X", "name": "ProcessMessages 1464", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110911987, "dur": 95, "ph": "X", "name": "ReadAsync 1464", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110912085, "dur": 2, "ph": "X", "name": "ProcessMessages 2086", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110912088, "dur": 143, "ph": "X", "name": "ReadAsync 2086", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110912234, "dur": 2, "ph": "X", "name": "ProcessMessages 1174", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110912237, "dur": 287, "ph": "X", "name": "ReadAsync 1174", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110912527, "dur": 3, "ph": "X", "name": "ProcessMessages 3064", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110912531, "dur": 260, "ph": "X", "name": "ReadAsync 3064", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110912830, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110912866, "dur": 564, "ph": "X", "name": "ProcessMessages 1768", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914155, "dur": 115, "ph": "X", "name": "ReadAsync 1768", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914273, "dur": 6, "ph": "X", "name": "ProcessMessages 7326", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914280, "dur": 85, "ph": "X", "name": "ReadAsync 7326", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914370, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914495, "dur": 1, "ph": "X", "name": "ProcessMessages 1039", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914497, "dur": 48, "ph": "X", "name": "ReadAsync 1039", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914549, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914551, "dur": 35, "ph": "X", "name": "ReadAsync 497", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914591, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914784, "dur": 94, "ph": "X", "name": "ReadAsync 261", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914880, "dur": 2, "ph": "X", "name": "ProcessMessages 2609", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110914884, "dur": 345, "ph": "X", "name": "ReadAsync 2609", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915233, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915235, "dur": 52, "ph": "X", "name": "ReadAsync 440", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915289, "dur": 3, "ph": "X", "name": "ProcessMessages 4340", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915293, "dur": 147, "ph": "X", "name": "ReadAsync 4340", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915505, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915507, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915628, "dur": 2, "ph": "X", "name": "ProcessMessages 1495", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915631, "dur": 40, "ph": "X", "name": "ReadAsync 1495", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915675, "dur": 2, "ph": "X", "name": "ProcessMessages 1279", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915678, "dur": 29, "ph": "X", "name": "ReadAsync 1279", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915709, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915711, "dur": 33, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915746, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915748, "dur": 30, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915856, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915858, "dur": 25, "ph": "X", "name": "ReadAsync 386", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915901, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915967, "dur": 26, "ph": "X", "name": "ReadAsync 906", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915997, "dur": 1, "ph": "X", "name": "ProcessMessages 1203", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110915999, "dur": 165, "ph": "X", "name": "ReadAsync 1203", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110916168, "dur": 39, "ph": "X", "name": "ReadAsync 123", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110916209, "dur": 2, "ph": "X", "name": "ProcessMessages 1303", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110916212, "dur": 91, "ph": "X", "name": "ReadAsync 1303", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110916305, "dur": 57, "ph": "X", "name": "ProcessMessages 333", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110916364, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110916386, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110917803, "dur": 183, "ph": "X", "name": "ReadAsync 850", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110917988, "dur": 11, "ph": "X", "name": "ProcessMessages 13597", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918000, "dur": 17, "ph": "X", "name": "ReadAsync 13597", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918020, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918023, "dur": 32, "ph": "X", "name": "ReadAsync 598", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918060, "dur": 28, "ph": "X", "name": "ReadAsync 170", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918091, "dur": 37, "ph": "X", "name": "ReadAsync 264", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918131, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918133, "dur": 160, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918295, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918298, "dur": 35, "ph": "X", "name": "ReadAsync 925", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918336, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918338, "dur": 18, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918359, "dur": 111, "ph": "X", "name": "ReadAsync 67", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918475, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918511, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918513, "dur": 30, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918545, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918547, "dur": 228, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918779, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918781, "dur": 104, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918887, "dur": 2, "ph": "X", "name": "ProcessMessages 1893", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918890, "dur": 22, "ph": "X", "name": "ReadAsync 1893", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918914, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110918916, "dur": 91, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919010, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919012, "dur": 32, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919045, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919047, "dur": 93, "ph": "X", "name": "ReadAsync 565", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919145, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919240, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919243, "dur": 327, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919574, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919576, "dur": 33, "ph": "X", "name": "ReadAsync 505", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919610, "dur": 1, "ph": "X", "name": "ProcessMessages 1422", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919612, "dur": 307, "ph": "X", "name": "ReadAsync 1422", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110919922, "dur": 117, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920043, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920087, "dur": 2, "ph": "X", "name": "ProcessMessages 2181", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920091, "dur": 68, "ph": "X", "name": "ReadAsync 2181", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920161, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920163, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920181, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920198, "dur": 9, "ph": "X", "name": "ReadAsync 220", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920210, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920319, "dur": 52, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920373, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920459, "dur": 1, "ph": "X", "name": "ProcessMessages 1190", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920462, "dur": 342, "ph": "X", "name": "ReadAsync 1190", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920807, "dur": 2, "ph": "X", "name": "ProcessMessages 1083", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920810, "dur": 92, "ph": "X", "name": "ReadAsync 1083", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920905, "dur": 1, "ph": "X", "name": "ProcessMessages 882", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110920908, "dur": 32, "ph": "X", "name": "ReadAsync 882", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921046, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921049, "dur": 96, "ph": "X", "name": "ReadAsync 147", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921147, "dur": 1, "ph": "X", "name": "ProcessMessages 1164", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921149, "dur": 29, "ph": "X", "name": "ReadAsync 1164", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921181, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921183, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921211, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921213, "dur": 75, "ph": "X", "name": "ReadAsync 139", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921291, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921320, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921321, "dur": 22, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921347, "dur": 24, "ph": "X", "name": "ReadAsync 146", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921374, "dur": 77, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921454, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921456, "dur": 23, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921481, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921537, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921561, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921563, "dur": 142, "ph": "X", "name": "ReadAsync 753", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921709, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921747, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110921971, "dur": 39, "ph": "X", "name": "ReadAsync 525", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922012, "dur": 1, "ph": "X", "name": "ProcessMessages 1231", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922014, "dur": 30, "ph": "X", "name": "ReadAsync 1231", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922047, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922049, "dur": 91, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922198, "dur": 1, "ph": "X", "name": "ProcessMessages 130", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922200, "dur": 90, "ph": "X", "name": "ReadAsync 130", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922293, "dur": 2, "ph": "X", "name": "ProcessMessages 1528", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922296, "dur": 25, "ph": "X", "name": "ReadAsync 1528", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922323, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922325, "dur": 93, "ph": "X", "name": "ReadAsync 146", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922422, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922455, "dur": 23, "ph": "X", "name": "ReadAsync 550", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922481, "dur": 1, "ph": "X", "name": "ProcessMessages 94", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922483, "dur": 146, "ph": "X", "name": "ReadAsync 94", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922680, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922682, "dur": 41, "ph": "X", "name": "ReadAsync 447", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922725, "dur": 2, "ph": "X", "name": "ProcessMessages 1409", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922729, "dur": 151, "ph": "X", "name": "ReadAsync 1409", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922884, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922929, "dur": 1, "ph": "X", "name": "ProcessMessages 1030", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110922932, "dur": 67, "ph": "X", "name": "ReadAsync 1030", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923002, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923034, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923035, "dur": 29, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923066, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923068, "dur": 29, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923148, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923151, "dur": 30, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923226, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923228, "dur": 80, "ph": "X", "name": "ReadAsync 138", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923310, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923312, "dur": 80, "ph": "X", "name": "ReadAsync 268", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923395, "dur": 1, "ph": "X", "name": "ProcessMessages 1248", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923397, "dur": 71, "ph": "X", "name": "ReadAsync 1248", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923470, "dur": 1, "ph": "X", "name": "ProcessMessages 1226", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923473, "dur": 14, "ph": "X", "name": "ReadAsync 1226", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923489, "dur": 9, "ph": "X", "name": "ReadAsync 177", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923500, "dur": 112, "ph": "X", "name": "ReadAsync 63", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923617, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923649, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923651, "dur": 30, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923684, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923686, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923712, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923714, "dur": 133, "ph": "X", "name": "ReadAsync 228", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923851, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923871, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923873, "dur": 9, "ph": "X", "name": "ReadAsync 794", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923883, "dur": 8, "ph": "X", "name": "ReadAsync 177", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110923894, "dur": 105, "ph": "X", "name": "ReadAsync 63", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924002, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924152, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924154, "dur": 36, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924193, "dur": 1, "ph": "X", "name": "ProcessMessages 788", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924195, "dur": 29, "ph": "X", "name": "ReadAsync 788", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924228, "dur": 33, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924264, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924266, "dur": 75, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924834, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924837, "dur": 61, "ph": "X", "name": "ReadAsync 561", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924901, "dur": 3, "ph": "X", "name": "ProcessMessages 3001", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924905, "dur": 40, "ph": "X", "name": "ReadAsync 3001", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924950, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924984, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110924987, "dur": 80, "ph": "X", "name": "ReadAsync 537", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110925068, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110925070, "dur": 61, "ph": "X", "name": "ReadAsync 63", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110925134, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110925167, "dur": 26, "ph": "X", "name": "ReadAsync 195", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110925197, "dur": 88, "ph": "X", "name": "ReadAsync 193", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110925290, "dur": 953, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926322, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926325, "dur": 72, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926400, "dur": 4, "ph": "X", "name": "ProcessMessages 4397", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926406, "dur": 41, "ph": "X", "name": "ReadAsync 4397", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926452, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926492, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926495, "dur": 36, "ph": "X", "name": "ReadAsync 653", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926534, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926536, "dur": 22, "ph": "X", "name": "ReadAsync 525", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926562, "dur": 189, "ph": "X", "name": "ReadAsync 134", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926756, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926777, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926779, "dur": 34, "ph": "X", "name": "ReadAsync 548", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926817, "dur": 84, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926903, "dur": 1, "ph": "X", "name": "ProcessMessages 115", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926905, "dur": 19, "ph": "X", "name": "ReadAsync 115", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926927, "dur": 37, "ph": "X", "name": "ReadAsync 143", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110926985, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927029, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927032, "dur": 133, "ph": "X", "name": "ReadAsync 760", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927167, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927170, "dur": 27, "ph": "X", "name": "ReadAsync 541", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927201, "dur": 168, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927371, "dur": 2, "ph": "X", "name": "ProcessMessages 1163", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927374, "dur": 103, "ph": "X", "name": "ReadAsync 1163", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927480, "dur": 1, "ph": "X", "name": "ProcessMessages 935", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927482, "dur": 30, "ph": "X", "name": "ReadAsync 935", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927515, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927517, "dur": 114, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927636, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927799, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927868, "dur": 79, "ph": "X", "name": "ReadAsync 508", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927951, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927983, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110927985, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110928046, "dur": 546, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110928596, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110928618, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110928620, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110928762, "dur": 11, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110928775, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110928885, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110928888, "dur": 107, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110928999, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929026, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929028, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929105, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929163, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929166, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929292, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929324, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929326, "dur": 161, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929492, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929523, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929617, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929742, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929745, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929826, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929828, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929894, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110929897, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110930078, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110930080, "dur": 270, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110930362, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110930364, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110930464, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110930465, "dur": 462, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110930933, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110930960, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110930962, "dur": 70, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931036, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931120, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931122, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931157, "dur": 156, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931317, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931356, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931449, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931451, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931991, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110931993, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110932049, "dur": 216, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110932269, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110932352, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110932354, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110932574, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110932605, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110932606, "dur": 246, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110932866, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933105, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933107, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933177, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933179, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933223, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933302, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933304, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933337, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933403, "dur": 194, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933657, "dur": 24, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933684, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933829, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933831, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933862, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110933864, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934141, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934143, "dur": 182, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934367, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934370, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934407, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934409, "dur": 235, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934648, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934692, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934694, "dur": 101, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934800, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934837, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934946, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934974, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110934976, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935178, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935180, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935320, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935338, "dur": 69, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935412, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935448, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935622, "dur": 130, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935755, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935757, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935822, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935825, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935892, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110935894, "dur": 332, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110936231, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110936369, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110936371, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110936447, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110936450, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110936501, "dur": 420, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110936965, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110936985, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937026, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937028, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937054, "dur": 265, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937322, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937324, "dur": 363, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937692, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937712, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937802, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937804, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110937851, "dur": 356, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110938212, "dur": 260, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110938475, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110938477, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110938699, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110938701, "dur": 193, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110938897, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110938900, "dur": 86, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110938991, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939021, "dur": 153, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939230, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939308, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939310, "dur": 64, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939380, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939405, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939406, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939542, "dur": 177, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939722, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939724, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939836, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939838, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939879, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939892, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939933, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110939951, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940051, "dur": 191, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940245, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940247, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940317, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940409, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940411, "dur": 244, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940658, "dur": 85, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940746, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940837, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110940840, "dur": 217, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941061, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941080, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941168, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941200, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941202, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941347, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941459, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941462, "dur": 170, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941636, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941683, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941685, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941715, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941867, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941898, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941900, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110941997, "dur": 252, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942252, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942254, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942273, "dur": 104, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942381, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942410, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942412, "dur": 84, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942499, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942501, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942589, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110942633, "dur": 638, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110943276, "dur": 1036, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110944315, "dur": 639, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110944957, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945060, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945063, "dur": 325, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945393, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945434, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945436, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945566, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945625, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945718, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945720, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945861, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945863, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110945928, "dur": 389, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110946322, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110946384, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110946497, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110946575, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110946578, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110946895, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110946898, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110946919, "dur": 226, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110947149, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110947191, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110947223, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110947225, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110947442, "dur": 264, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110947708, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110947710, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110947791, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110947794, "dur": 355, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948154, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948172, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948174, "dur": 30, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948207, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948280, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948470, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948487, "dur": 151, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948645, "dur": 214, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948907, "dur": 12, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948923, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110948989, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949031, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949033, "dur": 211, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949248, "dur": 151, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949402, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949404, "dur": 17, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949425, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949488, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949515, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949739, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949761, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949799, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949881, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110949883, "dur": 398, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950302, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950304, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950411, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950413, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950442, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950443, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950547, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950806, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950809, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950863, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950866, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950942, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110950962, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110951043, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110951073, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110951282, "dur": 147, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110951526, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110951858, "dur": 83, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110952083, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110952135, "dur": 170, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110952359, "dur": 155, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110952601, "dur": 313, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110952977, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110953191, "dur": 183, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110953475, "dur": 7, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110953577, "dur": 425, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110954171, "dur": 127, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110954310, "dur": 126, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110954541, "dur": 98, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110954642, "dur": 315, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110954961, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110954963, "dur": 1135, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110956196, "dur": 166, "ph": "X", "name": "ProcessMessages 196", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110956564, "dur": 114, "ph": "X", "name": "ReadAsync 196", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110956808, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110956812, "dur": 146, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110956961, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110956963, "dur": 220, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110957330, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110957332, "dur": 232, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110957953, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110958048, "dur": 692, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110959653, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110959659, "dur": 589, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110961356, "dur": 681, "ph": "X", "name": "ProcessMessages 272", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110963315, "dur": 4763, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110968693, "dur": 62, "ph": "X", "name": "ProcessMessages 832", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110968827, "dur": 15383, "ph": "X", "name": "ReadAsync 832", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110984217, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110984400, "dur": 326, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110984867, "dur": 2968, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110989159, "dur": 995, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110990572, "dur": 348, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110991118, "dur": 631, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110993093, "dur": 299, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110993740, "dur": 1671, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110995531, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352110995533, "dur": 2169, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111009256, "dur": 57, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111009846, "dur": 4871, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111015083, "dur": 7, "ph": "X", "name": "ProcessMessages 804", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111015289, "dur": 3098, "ph": "X", "name": "ReadAsync 804", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111018790, "dur": 119, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111018912, "dur": 6282, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111025477, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111025481, "dur": 954, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111026621, "dur": 95, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111026814, "dur": 743, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111027864, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111027958, "dur": 4878, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111037442, "dur": 23673, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111061561, "dur": 3182, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111064747, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111064751, "dur": 4752, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111069710, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111069717, "dur": 7164, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111076999, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111077136, "dur": 1045, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111078184, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111078188, "dur": 10287, "ph": "X", "name": "ReadAsync 192", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111089028, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111089244, "dur": 4116, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111093621, "dur": 1486, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111095257, "dur": 480, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111095746, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111095748, "dur": 2708, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111098553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111098636, "dur": 759, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111099513, "dur": 5111, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111104861, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111105120, "dur": 80, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111105300, "dur": 4852, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111110164, "dur": 16, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111110209, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111110309, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111110312, "dur": 315, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111110703, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111110757, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111110985, "dur": 44, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111111031, "dur": 126, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111111188, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111111190, "dur": 5568, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111116938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111116941, "dur": 226, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111117208, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111117211, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111117286, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111117288, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111117552, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111117554, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111117748, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111117750, "dur": 632, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111118453, "dur": 320, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111118850, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111118852, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111119097, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111119100, "dur": 942, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111120046, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111120064, "dur": 806, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111120961, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111120963, "dur": 236, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111121201, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111121203, "dur": 139, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111121419, "dur": 867, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111122370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111122372, "dur": 317, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111122695, "dur": 1111, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111123949, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111123951, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111124055, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111124129, "dur": 706917, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111831053, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111831060, "dur": 259, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111831322, "dur": 585, "ph": "X", "name": "ProcessMessages 5368", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352111831910, "dur": 304082, "ph": "X", "name": "ReadAsync 5368", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352112136008, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352112136015, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352112136122, "dur": 51, "ph": "X", "name": "ReadAsync 52000", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352112136178, "dur": 30, "ph": "X", "name": "ProcessMessages 8610", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352112136211, "dur": 282520, "ph": "X", "name": "ReadAsync 8610", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352112418740, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352112418745, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352112418794, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352112418798, "dur": 669707, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113088514, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113088518, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113088553, "dur": 22, "ph": "X", "name": "ProcessMessages 499", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113088576, "dur": 95516, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113184101, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113184105, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113184134, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113184137, "dur": 1782, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113185925, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113185965, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113185985, "dur": 172117, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113358110, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113358114, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113358140, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113358143, "dur": 2177, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113360327, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113360350, "dur": 22, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352113360374, "dur": 2305115, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352115665497, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352115665502, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352115665537, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352115665541, "dur": 964458, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116630007, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116630010, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116630042, "dur": 19, "ph": "X", "name": "ProcessMessages 5346", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116630062, "dur": 765, "ph": "X", "name": "ReadAsync 5346", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116630833, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116630877, "dur": 9, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116630888, "dur": 572, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116631465, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116631494, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 25769803776, "ts": 1754352116631496, "dur": 14645, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 54485999, "ts": 1754352116647271, "dur": 1743, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 21474836480, "ts": 1754352110851564, "dur": 1037796, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 21474836480, "ts": 1754352111889361, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 21474836480, "ts": 1754352111889363, "dur": 54, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 54485999, "ts": 1754352116649016, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 17179869184, "ts": 1754352110835049, "dur": 5811137, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 17179869184, "ts": 1754352110835789, "dur": 15740, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 17179869184, "ts": 1754352116646201, "dur": 71, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 17179869184, "ts": 1754352116646216, "dur": 16, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 54485999, "ts": 1754352116649024, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754352110895461, "dur":3733, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352110899206, "dur":1532, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352110900895, "dur":82, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1754352110900977, "dur":653, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352110901848, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_EFFD41E4CE0B2FA5.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110902594, "dur":105, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8715379F8421C7EF.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110902857, "dur":102, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_909E97C48675646C.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110903036, "dur":86, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_70A0B51E6CE518CC.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110903673, "dur":104, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_41430D66473C6D80.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110903798, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_D4A801B76B5F0B73.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110904876, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_54050F91DBE64E14.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110904931, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1754352110904996, "dur":90, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1754352110906629, "dur":178, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1754352110907143, "dur":76, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1754352110908388, "dur":99, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Samples.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1754352110908940, "dur":253, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1754352110909337, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.Multiplayer.Center.Common.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754352110909918, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.Base.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754352110911741, "dur":84, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2E991303F724098A.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110911849, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_DE8684D531642BC7.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110912087, "dur":81, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1DE1478ED4BCCE77.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110913095, "dur":363, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_A2303167CD380901.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110913463, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_0A18D4A0F938414F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110914094, "dur":421, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_344BA52ABEE2DA22.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110914765, "dur":177, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0B454A1D767C04B9.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110914948, "dur":207, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DD7B4151305E403E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110915229, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_5ECE9F832F449886.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110915285, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_AFFBA473F8ED618F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110915562, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_12B30D161BCDB405.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110916758, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.UI.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1754352110916930, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1754352110918590, "dur":159, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.pdb" }}
,{ "pid":12345, "tid":0, "ts":1754352110918774, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/BakeryRuntimeAssembly.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1754352110919699, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Tayx.Graphy.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1754352110920349, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":0, "ts":1754352110920819, "dur":79, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":0, "ts":1754352110921247, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Csg.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754352110921341, "dur":107, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/16791050694966752453.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754352110922106, "dur":76, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1754352110922298, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Stl.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754352110925620, "dur":99, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1754352110925890, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":0, "ts":1754352110925948, "dur":111, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":0, "ts":1754352110927763, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1754352110901660, "dur":26745, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352110928422, "dur":5703191, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352116631614, "dur":224, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352116632090, "dur":97, "ph":"X", "name": "BuildQueueDestroyTail",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352116632230, "dur":2945, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754352110915105, "dur":13468, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110928574, "dur":813, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_B68F15CFE4268FD0.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110929388, "dur":779, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110930174, "dur":691, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_B43F3957A6B105E8.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110930866, "dur":736, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110931609, "dur":511, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_B3B4D2E52BBC96AA.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110932121, "dur":525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110932652, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_565B326DB1440AB9.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110932794, "dur":1202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110934001, "dur":531, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B70A721B6AC74CE3.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110934533, "dur":581, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110935119, "dur":327, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_C0FF02094BE36A81.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110935446, "dur":574, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110936034, "dur":445, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C8F0A6EA578D7454.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110936479, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110937074, "dur":681, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_DE8684D531642BC7.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110937755, "dur":1147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110938929, "dur":245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110939189, "dur":730, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110939925, "dur":372, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_39160E6B4D490F3F.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110940298, "dur":840, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110941144, "dur":468, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_41430D66473C6D80.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110941613, "dur":440, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110942059, "dur":361, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_7B581C91A877B99A.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110942420, "dur":277, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110942705, "dur":627, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C3A52E6AAE7356A0.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110943333, "dur":425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110943764, "dur":1974, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_A95EB2FBD8B50DB2.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110945739, "dur":637, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110946383, "dur":542, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_6615B29F73AA9CEC.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110946925, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110947078, "dur":235, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_501785E1D43B029D.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110947314, "dur":889, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110948209, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110948385, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110948954, "dur":1216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110950177, "dur":1069, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110951276, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110951894, "dur":501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110952401, "dur":382, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110952791, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110953357, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110953907, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110954496, "dur":487, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110954988, "dur":742, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110955736, "dur":526, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110956268, "dur":632, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110956906, "dur":629, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110957544, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110958180, "dur":485, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110958693, "dur":451, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110959149, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110959668, "dur":734, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110960407, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110960991, "dur":1098, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110962112, "dur":1470, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110963609, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110964342, "dur":458, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110966560, "dur":5180, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\PlayerController\\ShmovementSystem\\GrapplingHook\\PredictionVisualizer.cs" }}
,{ "pid":12345, "tid":1, "ts":1754352110972168, "dur":640, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\PlayerController\\RagdollTumbleSystem.cs" }}
,{ "pid":12345, "tid":1, "ts":1754352110964804, "dur":8505, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110973579, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxNull.cs" }}
,{ "pid":12345, "tid":1, "ts":1754352110974113, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxNodeAttribute.cs" }}
,{ "pid":12345, "tid":1, "ts":1754352110974689, "dur":697, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxNode.cs" }}
,{ "pid":12345, "tid":1, "ts":1754352110975387, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxMesh.cs" }}
,{ "pid":12345, "tid":1, "ts":1754352110976053, "dur":570, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxMatrix.cs" }}
,{ "pid":12345, "tid":1, "ts":1754352110976955, "dur":650, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxManager.cs" }}
,{ "pid":12345, "tid":1, "ts":1754352110979807, "dur":910, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxLayerElementTangent.cs" }}
,{ "pid":12345, "tid":1, "ts":1754352110973309, "dur":8069, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110981378, "dur":772, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Xml.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110982150, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110982690, "dur":1477, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110984649, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110985372, "dur":890, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110986603, "dur":1271, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110987874, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110988502, "dur":889, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110989852, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110990387, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110991491, "dur":1605, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110993096, "dur":813, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352110981378, "dur":13909, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110995288, "dur":224, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352110995512, "dur":237, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352110995755, "dur":103006, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754352111098763, "dur":425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111099207, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111099361, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352111099448, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111099589, "dur":1052, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754352111100641, "dur":405, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111101058, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111101295, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352111101403, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111101727, "dur":592, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754352111102320, "dur":487, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111102819, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111102993, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754352111103103, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111103280, "dur":534, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754352111104513, "dur":140, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352112136620, "dur":223, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352111106652, "dur":1032422, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754352112177003, "dur":238858, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aP.dag\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352112176899, "dur":240672, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1754352112419161, "dur":283, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754352112420700, "dur":668553, "ph":"X", "name": "ILPostProcess",  "args": { "detail":"Library/Bee/artifacts/1900b0aP.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1754352113181761, "dur":177052, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aP.dag\\post-processed\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352113181656, "dur":177159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352113358838, "dur":2249, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754352113361101, "dur":3270513, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110902185, "dur":26268, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110928460, "dur":996, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_AFFBA473F8ED618F.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110929457, "dur":898, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110930402, "dur":519, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_240082260DA46DB1.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110930921, "dur":723, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110931649, "dur":637, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_741A091E1D31783E.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110932286, "dur":1277, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110933570, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_BB861107624D88A6.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110933721, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110934334, "dur":482, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_95C8A01F14B2B1A7.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110934816, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110935407, "dur":415, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_9CAA34D813E1D86C.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110935823, "dur":571, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110936399, "dur":443, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_5D2FEA495BF1EC5D.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110936843, "dur":833, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110937681, "dur":884, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_12B30D161BCDB405.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110938566, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110939182, "dur":155, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_12B30D161BCDB405.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110939339, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.TestRunner.rsp" }}
,{ "pid":12345, "tid":2, "ts":1754352110939394, "dur":726, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110940142, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_26AE19A8AC5D7F29.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110940381, "dur":770, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110941194, "dur":1066, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_FE2E7DF300ABB7FB.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110942260, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110942727, "dur":2792, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B73D317803417DBD.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110945520, "dur":780, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110946300, "dur":70, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B73D317803417DBD.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110946372, "dur":629, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FF1B42937EB508E.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110947001, "dur":247, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110947273, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_80D04A5B7DD31995.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110947437, "dur":835, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110948278, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110948400, "dur":905, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110949315, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110949443, "dur":1015, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110950465, "dur":38776, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754352110989242, "dur":475, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110989729, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110989883, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352110989952, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352110990125, "dur":9520, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754352110999646, "dur":4032, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111003691, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111003836, "dur":604, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754352111004440, "dur":1760, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111006206, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352111006282, "dur":198, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111006488, "dur":1437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111007930, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Stl.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754352111007996, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111008408, "dur":300, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Stl.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754352111008709, "dur":4475, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111014467, "dur":712, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-runtime-l1-1-0.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111016056, "dur":785, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-multibyte-l1-1-0.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111016841, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-math-l1-1-0.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111017469, "dur":1249, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-locale-l1-1-0.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111019542, "dur":505, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-environment-l1-1-0.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111021648, "dur":1426, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-sysinfo-l1-1-0.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111023074, "dur":2215, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-2-0.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111025289, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-1-0.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111013188, "dur":12983, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111026172, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\GUI\\Views\\TestListGUIBase.cs" }}
,{ "pid":12345, "tid":2, "ts":1754352111026727, "dur":43732, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\GUI\\UITestRunnerFilter.cs" }}
,{ "pid":12345, "tid":2, "ts":1754352111070459, "dur":1168, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\GUI\\TestRunnerUIFilter.cs" }}
,{ "pid":12345, "tid":2, "ts":1754352111072698, "dur":40523, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\GUI\\TestListBuilder\\TestTreeViewBuilder.cs" }}
,{ "pid":12345, "tid":2, "ts":1754352111026172, "dur":87056, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111113228, "dur":516962, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111630192, "dur":1660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1754352111631852, "dur":2727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111634585, "dur":1453, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Autodesk.Fbx.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1754352111636039, "dur":2785, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111638829, "dur":1211, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1754352111640041, "dur":2102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111642151, "dur":762, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111642956, "dur":8983, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111651943, "dur":1426, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111653387, "dur":12877, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":2, "ts":1754352111666269, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111666880, "dur":7568, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":2, "ts":1754352111674468, "dur":1175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111675664, "dur":5988, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":2, "ts":1754352111681657, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111682279, "dur":5654, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.pdb" }}
,{ "pid":12345, "tid":2, "ts":1754352111687934, "dur":214729, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754352111902663, "dur":4728958, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110902223, "dur":26237, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110928469, "dur":951, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_5ECE9F832F449886.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110929421, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110930059, "dur":553, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_3BD0E521496D18C3.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110930613, "dur":374, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110930991, "dur":300, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_CAD8565B77F30E0B.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110931292, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110931988, "dur":789, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_58C3C932C71A1CBF.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110932778, "dur":1020, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110933803, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_A2A2C6B2311CC341.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110933973, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110934433, "dur":369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_F062596BB2A95F2B.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110934802, "dur":748, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110935557, "dur":521, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_9A8E92A8EFCECC8D.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110936078, "dur":414, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110936496, "dur":500, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_9C5204C43A585DAC.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110936996, "dur":446, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110937447, "dur":178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C9D5C17E5033BA03.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110937625, "dur":1463, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110939100, "dur":325, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110939432, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_8914E4E9F3EE50C1.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110939575, "dur":1040, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110940620, "dur":899, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E4271FF7F123ABE1.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110941520, "dur":292, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110941812, "dur":57, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E4271FF7F123ABE1.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110942122, "dur":490, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_AA52405A8165CEAD.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110942613, "dur":376, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110942994, "dur":617, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F80DC665559BD2A7.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110943611, "dur":2528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110946177, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF8AAC9A6A267565.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110946342, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110946657, "dur":857, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_8621ABC4F033EAB6.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110947514, "dur":1139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110948707, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":3, "ts":1754352110948759, "dur":355, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110949146, "dur":1154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110950313, "dur":1148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110951467, "dur":915, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110952396, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110952984, "dur":733, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110953730, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110959548, "dur":25510, "ph":"X", "name": "WriteResponseFile",  "args": { "detail":"Library/Bee/artifacts/rsp/4747638433968585886.rsp" }}
,{ "pid":12345, "tid":3, "ts":1754352110985923, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110986484, "dur":649, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110987134, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110987822, "dur":501, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Memory.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110988754, "dur":532, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110990356, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110990858, "dur":789, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110991647, "dur":1281, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Pipelines.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110992928, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110993603, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352110985060, "dur":10493, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110995555, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754352110995621, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352110995841, "dur":9039, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1754352111004881, "dur":1360, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111006251, "dur":1053, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111007314, "dur":930, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111010334, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Console.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111008254, "dur":4628, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111014316, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Extensions.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111015823, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cryptography.KeyDerivation.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111016352, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cryptography.Internal.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111017014, "dur":1356, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cors.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111019159, "dur":842, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Web.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111021687, "dur":927, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.Policy.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111022615, "dur":1113, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111023728, "dur":1121, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.OAuth.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111012882, "dur":11968, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111024850, "dur":3544, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111030738, "dur":2522, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\Api\\CallbacksHolder.cs" }}
,{ "pid":12345, "tid":3, "ts":1754352111033261, "dur":37350, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\Api\\CallbacksDelegator.cs" }}
,{ "pid":12345, "tid":3, "ts":1754352111070612, "dur":6050, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\Api\\Analytics\\TestTreeData.cs" }}
,{ "pid":12345, "tid":3, "ts":1754352111028394, "dur":48851, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111077655, "dur":976, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\PostProcessing\\Components\\ChromaticAberration.cs" }}
,{ "pid":12345, "tid":3, "ts":1754352111077275, "dur":4535, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111083242, "dur":930, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Lighting\\Reflection\\HDAdditionalReflectionData.Legacy.cs" }}
,{ "pid":12345, "tid":3, "ts":1754352111085233, "dur":29969, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Lighting\\LightCookieManager.cs" }}
,{ "pid":12345, "tid":3, "ts":1754352111081811, "dur":33396, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111115208, "dur":516733, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111631942, "dur":1318, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1754352111633261, "dur":2299, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111635566, "dur":1332, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.Poly2Tri.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1754352111636898, "dur":410, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111637313, "dur":1290, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Mathematics.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1754352111638603, "dur":2736, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111641346, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111641892, "dur":8609, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111650506, "dur":752, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111651270, "dur":9569, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111660845, "dur":1164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111662035, "dur":7043, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":3, "ts":1754352111669084, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111669701, "dur":7570, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":3, "ts":1754352111677276, "dur":465, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111677764, "dur":6550, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":3, "ts":1754352111684320, "dur":776, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754352111685109, "dur":5570, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.pdb" }}
,{ "pid":12345, "tid":3, "ts":1754352111690680, "dur":4940937, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110902121, "dur":26310, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110928461, "dur":236, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110928713, "dur":451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_AF5867BA518896C5.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110929165, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110929579, "dur":166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_105C003939CBB5BC.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110929746, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110930450, "dur":440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0E92FB74A3F3A210.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110930891, "dur":739, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110931636, "dur":503, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_E40A164F9128DA90.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110932140, "dur":725, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110932873, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_6AC9873B162562B4.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110932993, "dur":885, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110933883, "dur":339, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_79FA45288888936E.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110934223, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110934503, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_A30BE990D5494330.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110934807, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110935343, "dur":261, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1D1266FB84838992.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110935605, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110936166, "dur":355, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_41CD022C3A77A517.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110936522, "dur":373, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110936900, "dur":275, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E802E0B7E2FE30F3.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110937176, "dur":603, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110937785, "dur":882, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0ECD44D8296CBFC9.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110938668, "dur":467, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110939146, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110939380, "dur":516, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110939904, "dur":433, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7C305B000634EB8F.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110940337, "dur":763, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110941105, "dur":514, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_F89343DAF7FCE930.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110941620, "dur":477, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110942102, "dur":663, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_03E991028114FC34.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110942779, "dur":330, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110943116, "dur":544, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4C8FCB1634F995AA.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110943660, "dur":1715, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110945382, "dur":414, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_0CEBA14B38E03A37.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110945796, "dur":658, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110946459, "dur":585, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FD791905E1680711.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110947045, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110947442, "dur":190, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_6DD844CF5FED2D78.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110947633, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110948286, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110948421, "dur":513, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110948962, "dur":988, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110949986, "dur":522, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110950514, "dur":972, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110951495, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110951960, "dur":489, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110952484, "dur":475, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110952969, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110953471, "dur":498, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110953983, "dur":563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110954551, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110955052, "dur":458, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110955517, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110956123, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110956712, "dur":454, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110957175, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110957689, "dur":737, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110958450, "dur":476, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110958978, "dur":442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110959455, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110960026, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110960596, "dur":748, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110961391, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110962089, "dur":1078, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110963190, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110963882, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110964397, "dur":459, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110967349, "dur":1260, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Inv\\InvEquipmentManager.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352110968609, "dur":855, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Inv\\InvDroppedStorageEquipment.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352110969810, "dur":810, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Inv\\InvDraggedItemVisual.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352110964860, "dur":5761, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110970621, "dur":1755, "ph":"X", "name": "File",  "args": { "detail":"Assets\\Bakery\\ftLocalStorage.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352110972376, "dur":12077, "ph":"X", "name": "File",  "args": { "detail":"Assets\\Bakery\\ftLightmapsStorage.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352110984454, "dur":1245, "ph":"X", "name": "File",  "args": { "detail":"Assets\\Bakery\\ftLightmaps.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352110970621, "dur":17757, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110988755, "dur":504, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352110991916, "dur":1280, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352110993201, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352110995473, "dur":1475, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352110997997, "dur":1054, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352110988379, "dur":10672, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352110999053, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352110999125, "dur":1551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111000682, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352111000759, "dur":2788, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111003552, "dur":420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754352111003973, "dur":2134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111006114, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754352111006197, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111006393, "dur":451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754352111006845, "dur":478, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111007333, "dur":917, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111008258, "dur":3942, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111014443, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.TagHelpers.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352111016234, "dur":718, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352111016952, "dur":1143, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Json.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352111019317, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Core.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352111012201, "dur":8168, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111020369, "dur":3183, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111024802, "dur":1808, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\Tasks\\EnableTestOutLoggerTask.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352111028622, "dur":836, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\TaskList.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352111023552, "dur":6034, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111029942, "dur":2992, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352111029587, "dur":5014, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111034602, "dur":2687, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111037289, "dur":24488, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Core\\SelectionPickerRenderer.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352111064586, "dur":1922, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Core\\PivotPoint.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352111037289, "dur":29864, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111067154, "dur":2803, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111069958, "dur":1491, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754352111071450, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111071816, "dur":225, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111073407, "dur":1817, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Settings\\HDRPDefaultVolumeProfileSetting.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352111072047, "dur":4477, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111076695, "dur":1287, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\HDRenderPipeline.LookDev.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352111076525, "dur":5042, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111081567, "dur":3141, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111084709, "dur":2507, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111088798, "dur":823, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Distance.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352111087217, "dur":2976, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111090193, "dur":2734, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111092962, "dur":2910, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111096239, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticFunctionInvoker_5.cs" }}
,{ "pid":12345, "tid":4, "ts":1754352111095873, "dur":2583, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111098457, "dur":531825, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111630283, "dur":1564, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754352111631847, "dur":1979, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111633833, "dur":1378, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Timeline.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754352111635212, "dur":1565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111636782, "dur":1626, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754352111638409, "dur":838, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111639253, "dur":1312, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754352111640566, "dur":1510, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111642084, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111642278, "dur":5509, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352111647791, "dur":1129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111648932, "dur":6373, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352111655310, "dur":598, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111655920, "dur":6072, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352111662004, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111662530, "dur":9439, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754352111671974, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111672526, "dur":7196, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.pdb" }}
,{ "pid":12345, "tid":4, "ts":1754352111679727, "dur":782, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111680541, "dur":6174, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Recorder.pdb" }}
,{ "pid":12345, "tid":4, "ts":1754352111686720, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754352111687341, "dur":3619, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.pdb" }}
,{ "pid":12345, "tid":4, "ts":1754352111690962, "dur":4940671, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110902124, "dur":26321, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110928461, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110928761, "dur":398, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_4D3FF430C4DBEDA6.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110929159, "dur":345, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110929509, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BE56795A9D79E8CF.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110929703, "dur":761, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110930468, "dur":443, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_F61584CA640D6CCE.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110930912, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110931605, "dur":396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_A2303167CD380901.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110932002, "dur":208, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110932213, "dur":467, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FB94EDC2B6010139.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110932681, "dur":1066, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110933752, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_15651534C63A846D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110933881, "dur":375, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110934263, "dur":401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_9A9731BDE814CF5D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110934664, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110935157, "dur":217, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_5C1AD996009448CF.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110935375, "dur":640, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110936022, "dur":368, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_537703C7D88D2F6E.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110936391, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110936886, "dur":261, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2E991303F724098A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110937148, "dur":399, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110937552, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4359553D78AACF49.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110937669, "dur":854, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110938528, "dur":387, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_584F14C06FD5B328.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110938916, "dur":809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110939733, "dur":356, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DEAC990A9FF99A9D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110940090, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110940702, "dur":420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_FD2466E023205425.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110941123, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110941485, "dur":289, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_B855F2D639BFAFEA.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110941775, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110942367, "dur":504, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_62A195EBC63037A9.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110942872, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110943118, "dur":1994, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_70A0B51E6CE518CC.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110945113, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110945438, "dur":361, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5E54B59758C12815.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110945800, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110946335, "dur":59, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5E54B59758C12815.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110946396, "dur":665, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_C1748450BA1FE4A2.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110947062, "dur":406, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110947474, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_77C2F24B6E7C428A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110947662, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110948336, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110948463, "dur":225, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_35BBE1C597B25ECF.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110948689, "dur":342, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110949041, "dur":1180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110950235, "dur":926, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110951178, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110951816, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110952387, "dur":444, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110952836, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110953365, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110953921, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110954512, "dur":444, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110954966, "dur":453, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110955446, "dur":482, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110955933, "dur":504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110956442, "dur":519, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110956966, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110957470, "dur":526, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110958007, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110958598, "dur":424, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110959028, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110959585, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110960189, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110960816, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110961391, "dur":647, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110962038, "dur":259, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10262047607611578282.rsp" }}
,{ "pid":12345, "tid":5, "ts":1754352110962320, "dur":924, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110963265, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110964001, "dur":480, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110964504, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110967787, "dur":1582, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Interface\\BatteryController.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352110969685, "dur":1192, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Interact\\InteractionManager.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352110965070, "dur":6523, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110974128, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxStatus.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352110975051, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxSkeleton.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352110975693, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxSharpProgressCallback.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352110976230, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxShape.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352110976857, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxSemanticEntryView.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352110977954, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxQuaternion.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352110971594, "dur":7130, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110981079, "dur":970, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110982049, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110982686, "dur":1488, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Xml.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110985342, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110985857, "dur":796, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110986653, "dur":888, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Transactions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110987858, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110978725, "dur":9793, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110989002, "dur":519, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110991561, "dur":584, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\msquic.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110992145, "dur":1071, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\mscorrc.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110993216, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\mscorlib.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110995519, "dur":1543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110997602, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.JSInterop.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110998175, "dur":1026, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.WebEncoders.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352110988518, "dur":10683, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352110999202, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProGrids.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352110999290, "dur":1315, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111000616, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352111000691, "dur":2751, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111003448, "dur":1111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1754352111004560, "dur":1693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111006257, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Tayx.Graphy.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352111006328, "dur":409, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111006746, "dur":640, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111007390, "dur":300, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Domain_Reload.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1754352111007691, "dur":3034, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111010731, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352111010813, "dur":2194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111013013, "dur":598, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1754352111013612, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111014021, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111014168, "dur":2271, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111016440, "dur":1774, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111018215, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754352111018285, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111018435, "dur":7056, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1754352111025493, "dur":374, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111025886, "dur":715, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111026734, "dur":6695, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\GUI\\IAssetsDatabaseHelper.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352111033430, "dur":37104, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\GUI\\GuiHelper.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352111026607, "dur":47062, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111075718, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\LineRendering\\Core\\LineRendering.ShadingAtlas.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352111073669, "dur":3013, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111077631, "dur":942, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\PostProcessing\\Components\\WhiteBalance.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352111076682, "dur":3812, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111080495, "dur":3476, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111083971, "dur":2537, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111086508, "dur":2209, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111089041, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\OnTriggerEnter.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352111090094, "dur":1009, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\OnCollisionEnter.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352111088718, "dur":3660, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111094903, "dur":1794, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Expression.cs" }}
,{ "pid":12345, "tid":5, "ts":1754352111092378, "dur":4575, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111096954, "dur":533232, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111630188, "dur":1658, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Burst.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754352111631847, "dur":2541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111634395, "dur":1404, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/UnityEngine.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754352111635800, "dur":1331, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111637139, "dur":1401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Recorder.Base.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754352111638541, "dur":1948, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111640497, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111640657, "dur":6637, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352111647300, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111647851, "dur":6837, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352111654694, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111655306, "dur":5550, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Domain_Reload.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352111660861, "dur":1077, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111661950, "dur":5253, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352111667208, "dur":1501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111668731, "dur":6164, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.pdb" }}
,{ "pid":12345, "tid":5, "ts":1754352111674899, "dur":1410, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111676321, "dur":6154, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.pdb" }}
,{ "pid":12345, "tid":5, "ts":1754352111682481, "dur":750, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352111683243, "dur":5563, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.pdb" }}
,{ "pid":12345, "tid":5, "ts":1754352111688807, "dur":1672292, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754352113361250, "dur":273770, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113635160, "dur":913, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113636210, "dur":733, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113637093, "dur":54402, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113691669, "dur":854, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113692637, "dur":92115, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113784917, "dur":98911, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113883978, "dur":69237, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113953365, "dur":708, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113954234, "dur":69469, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114023820, "dur":27016, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114050950, "dur":41535, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114092643, "dur":255287, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114348076, "dur":163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114348353, "dur":171, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114348652, "dur":44124, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114392906, "dur":670, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114393737, "dur":641, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114394470, "dur":491, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114395046, "dur":144787, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114539982, "dur":112323, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114652430, "dur":21690, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114674239, "dur":39502, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114713848, "dur":818, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114714776, "dur":92107, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114807030, "dur":809, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114807959, "dur":176969, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114985093, "dur":900, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114986123, "dur":787, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352114987069, "dur":159309, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352115146499, "dur":62913, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352115209548, "dur":109380, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352115319089, "dur":84798, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352115404031, "dur":95073, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352115499220, "dur":40624, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352115539979, "dur":126097, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":5, "ts":1754352113361100, "dur":2304987, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":5, "ts":1754352115667475, "dur":963293, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":6, "ts":1754352110915137, "dur":13518, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110928656, "dur":645, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_344BA52ABEE2DA22.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110929302, "dur":441, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110929752, "dur":410, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3531952820BFBB61.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110930162, "dur":411, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110930579, "dur":477, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_70068CD95C93665B.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110931057, "dur":806, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110931868, "dur":454, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_211BE83F68AA39BB.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110932324, "dur":1256, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110933586, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_C43BFCD1AD5E52A4.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110933729, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110934321, "dur":384, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_A9089B4625A0358A.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110934706, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110935290, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1DE1478ED4BCCE77.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110935417, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110936087, "dur":464, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_738FD04818385ADA.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110936551, "dur":397, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110936953, "dur":396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1A5537A35B9CE8F4.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110937349, "dur":1143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110938497, "dur":506, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_ED24D0B692D57D24.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110939004, "dur":1112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110940125, "dur":211, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_423A3D5EB868A3A3.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110940337, "dur":809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110941151, "dur":456, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_31A1A11DA6AFDA84.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110941608, "dur":462, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110942077, "dur":526, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_A82A5E8F52C8D895.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110942603, "dur":307, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110942915, "dur":674, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_0D705C99A10FA0DB.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110943590, "dur":1804, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110945401, "dur":463, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_5BB8ABC7FC50679E.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110945864, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110946478, "dur":706, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4B4960BD7841C36F.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110947185, "dur":759, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110947949, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_EFFD41E4CE0B2FA5.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110948101, "dur":349, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110948486, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110948681, "dur":1276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110949997, "dur":543, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110950554, "dur":951, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110951514, "dur":445, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110951969, "dur":481, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110952486, "dur":495, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110952987, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110953502, "dur":675, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110954189, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":6, "ts":1754352110954245, "dur":425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110954675, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110955234, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":6, "ts":1754352110955295, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110955911, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110956407, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110956962, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110957465, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110958053, "dur":873, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110958953, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110959488, "dur":720, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110960216, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110960711, "dur":632, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110961366, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110961782, "dur":1623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110963426, "dur":522, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110963972, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110964545, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110967907, "dur":4163, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Experimental\\MirrorOrbitCamera.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110972489, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Experimental\\FlairPool.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110965142, "dur":8551, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110973694, "dur":1428, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxLayerElementArrayTemplateInt.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110975123, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxLayerElementArrayTemplateFbxVector4.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110976239, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxLayerElementArrayTemplateFbxSurfaceMaterial.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110976778, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxLayerElementArrayTemplateFbxColor.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110977344, "dur":512, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxLayerElementArray.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110979688, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxIOFileHeaderInfo.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110980288, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxIOBase.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110981162, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxImplementation.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352110973693, "dur":8972, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110982666, "dur":1055, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110983722, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110984409, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110985555, "dur":802, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110986357, "dur":1174, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110987531, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110988042, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110988954, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110989527, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110990212, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110990791, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110991547, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110992126, "dur":1445, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110993571, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110994172, "dur":803, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110995423, "dur":1732, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110997155, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352110982666, "dur":15029, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352110997696, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.Base.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352110997765, "dur":2466, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111000239, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352111000308, "dur":3121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111003435, "dur":484, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1754352111003920, "dur":2111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111006075, "dur":460, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.Base.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1754352111006536, "dur":390, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111006941, "dur":872, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111007819, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Csg.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754352111007894, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111008445, "dur":306, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Csg.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1754352111008752, "dur":4785, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111013543, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-1-0.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352111013543, "dur":3293, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111016836, "dur":3351, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111020187, "dur":3222, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111023549, "dur":1031, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\Tasks\\ResetInteractionModeTask.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111024580, "dur":1393, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\Tasks\\RegisterFilesForCleanupVerificationTask.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111025973, "dur":872, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\Tasks\\PreparePlayModeRunTask.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111027302, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\Tasks\\Player\\DetermineRuntimePlatformTask.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111028907, "dur":3994, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\Tasks\\Platform\\PlatformSpecificCleanupTask.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111023410, "dur":10497, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111033908, "dur":43675, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.State\\StateEnterReason.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111033908, "dur":45783, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111079691, "dur":4248, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111084087, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Ports\\InvalidOutput.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111083940, "dur":3347, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111087399, "dur":5550, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\NumericComparison.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111095037, "dur":740, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Literal.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111087288, "dur":8888, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111096176, "dur":640, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\ReflectionFieldAccessor.cs" }}
,{ "pid":12345, "tid":6, "ts":1754352111096176, "dur":1505, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111097681, "dur":532507, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111630190, "dur":1660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1754352111631851, "dur":1903, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111633760, "dur":1550, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1754352111635311, "dur":1283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111636600, "dur":1607, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1754352111638208, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111638417, "dur":1324, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.TextMeshPro.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1754352111639741, "dur":1800, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111641550, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111641674, "dur":5870, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352111647550, "dur":381, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111647955, "dur":7464, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352111655425, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111656034, "dur":7697, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":6, "ts":1754352111663736, "dur":768, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111664518, "dur":5975, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.pdb" }}
,{ "pid":12345, "tid":6, "ts":1754352111670498, "dur":634, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111671145, "dur":5845, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.pdb" }}
,{ "pid":12345, "tid":6, "ts":1754352111676994, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111677628, "dur":5554, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":6, "ts":1754352111683187, "dur":519, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754352111683717, "dur":6853, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.pdb" }}
,{ "pid":12345, "tid":6, "ts":1754352111690571, "dur":4941038, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110915165, "dur":13467, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110928633, "dur":559, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_396FF69023EBF758.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110929193, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110929862, "dur":495, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F64D439B1EB03D69.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110930358, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110930782, "dur":441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_A217D4534C926E31.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110931223, "dur":886, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110932117, "dur":612, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_E157BC3631083DD4.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110932730, "dur":1236, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110933973, "dur":431, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_41B8564ACF28862B.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110934415, "dur":470, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110934892, "dur":245, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_D6EF53A96A575FB0.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110935138, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110935694, "dur":326, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_79435E9AE291CA71.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110936021, "dur":501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110936527, "dur":485, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D87855528AA5CBDB.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110937013, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110937618, "dur":384, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_418C07DCB2A70D27.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110938002, "dur":1126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110939143, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110939344, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110939494, "dur":1058, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110940559, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2FE329E4AC5AA183.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110940756, "dur":687, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110941449, "dur":331, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C7D11350F3D07FEB.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110941781, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110942426, "dur":408, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_7680C80A577CB114.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110942834, "dur":314, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110943155, "dur":1975, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_F25BBECE527C15FF.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110945131, "dur":476, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110945614, "dur":388, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_86C5A2E4AB98DFB3.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110946003, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110946504, "dur":584, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_EEF4899237036C91.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110947089, "dur":742, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110947839, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1D6B2DBA1E106A49.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110947965, "dur":489, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110948458, "dur":221, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_CBA0E82F75D57588.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110948680, "dur":362, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110949053, "dur":1126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110950191, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110951044, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.rsp" }}
,{ "pid":12345, "tid":7, "ts":1754352110951125, "dur":846, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110951976, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_65A8C0D1F9B322FF.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110952149, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110952322, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110952473, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110952666, "dur":593, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110953309, "dur":556, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110953899, "dur":593, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110954496, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110954994, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Runtime.rsp" }}
,{ "pid":12345, "tid":7, "ts":1754352110955053, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110955558, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":7, "ts":1754352110955619, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110956271, "dur":786, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110957063, "dur":506, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110957581, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110958140, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110958684, "dur":634, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110959323, "dur":334, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110959678, "dur":632, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110960315, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110960870, "dur":791, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110961689, "dur":1360, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110963055, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/17855849087711676260.rsp" }}
,{ "pid":12345, "tid":7, "ts":1754352110963113, "dur":1089, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110964222, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110964744, "dur":1448, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110968260, "dur":3613, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\AudioSystems\\UIAudioHandler.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352110972884, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\AudioSystems\\AudioEventChannel.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352110973781, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\External\\StlExporter\\Code\\pb_Stl_Importer.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352110966197, "dur":8125, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110974323, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxEuler.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352110974998, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxEntryView.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352110976202, "dur":632, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxDoubleTemplates.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352110979701, "dur":793, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxConstraintParent.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352110974322, "dur":8110, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110982433, "dur":621, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110983054, "dur":1385, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110984871, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110985379, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110986061, "dur":886, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110986947, "dur":837, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110987784, "dur":966, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110988750, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110989257, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110990288, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110990862, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110991510, "dur":1426, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110992936, "dur":1190, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110994522, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110995094, "dur":1047, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110996141, "dur":951, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352110982433, "dur":15090, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352110997525, "dur":305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.KdTree.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352110997831, "dur":3311, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111001147, "dur":1904, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.KdTree.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1754352111003052, "dur":3194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111006257, "dur":1076, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111007339, "dur":321, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Tayx.Graphy.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1754352111007660, "dur":5491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111014070, "dur":947, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.Cookies.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111016148, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\hostfxr.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111016793, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Grpc.Net.Common.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111017340, "dur":1126, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Grpc.Net.ClientFactory.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111019455, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.ClientFactory.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111021606, "dur":743, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\clretwrc.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111022350, "dur":822, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\aspnetcorev2_inprocess.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111023172, "dur":1684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-utility-l1-1-0.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111013161, "dur":11695, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111026466, "dur":1388, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestLaunchers\\Helpers\\FilePathMetaInfo.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111028266, "dur":4972, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestBuildAssemblyFilter.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111024857, "dur":9202, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111034072, "dur":146, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111034218, "dur":3876, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Shapes\\Torus.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111034218, "dur":7037, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111041256, "dur":16926, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Core\\MeshHandle.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111058370, "dur":852, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Core\\MaterialUtility.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111041256, "dur":20976, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111064582, "dur":1939, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Core\\ActionResult.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111062233, "dur":4411, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111066645, "dur":3107, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111069753, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352111069827, "dur":898, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111070731, "dur":698, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1754352111071430, "dur":1457, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111072899, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111073212, "dur":2016, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\Settings\\FrameSettingsHistory.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111076093, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\SceneViewDrawMode.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111076740, "dur":1023, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\RenderPass\\VrsCustomPass.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111077763, "dur":650, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\RenderPass\\UpscalerUtils.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111079361, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\RenderPass\\DrawRenderersCustomPass.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111079978, "dur":6124, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\RenderPass\\DLSSPass.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111086102, "dur":867, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\RenderPass\\DepthPyramidConstants.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111073036, "dur":14590, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111088989, "dur":1964, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\OnTriggerStay2D.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111087626, "dur":4556, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111092784, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Lists\\RemoveListItemAt.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111094341, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Lists\\AddListItem.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111095598, "dur":906, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Dictionaries\\MergeDictionaries.cs" }}
,{ "pid":12345, "tid":7, "ts":1754352111092182, "dur":4502, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111096684, "dur":23172, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111119857, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754352111119980, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111120210, "dur":525, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1754352111120736, "dur":757, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111121504, "dur":508689, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111630197, "dur":1649, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754352111631847, "dur":1897, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111633751, "dur":1380, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754352111635132, "dur":1934, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111637072, "dur":1407, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Domain_Reload.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754352111638480, "dur":1522, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111640008, "dur":1206, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Autodesk.Fbx.BuildTestAssets.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754352111641215, "dur":1051, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111642274, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111642925, "dur":4947, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111647880, "dur":2040, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111649932, "dur":7028, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111656966, "dur":459, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111657438, "dur":7121, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754352111664565, "dur":894, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111665473, "dur":5741, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":7, "ts":1754352111671218, "dur":1092, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111672324, "dur":5440, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Domain_Reload.pdb" }}
,{ "pid":12345, "tid":7, "ts":1754352111677769, "dur":1016, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111678797, "dur":5671, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb" }}
,{ "pid":12345, "tid":7, "ts":1754352111684474, "dur":1053, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754352111685541, "dur":5383, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":7, "ts":1754352111690926, "dur":4940693, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110903736, "dur":24796, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110928539, "dur":1007, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DD7B4151305E403E.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110929547, "dur":851, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110930407, "dur":498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_7E27985390166E89.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110930905, "dur":929, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110931840, "dur":572, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C3011450AA5BA1AD.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110932412, "dur":1410, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110933827, "dur":429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_3BBA69B756FFE45D.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110934256, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110934889, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_4B2F81998C42954D.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110935129, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110935783, "dur":393, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_BE8F3A603DA34520.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110936177, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110936573, "dur":359, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_039ACC1FFEFE8589.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110936933, "dur":1449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110938407, "dur":514, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_B52A3D999550438F.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110938921, "dur":1067, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110939993, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_E9E45E291217B73F.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110940144, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110940838, "dur":725, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_AE1E6954B6806C66.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110941563, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110941942, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_AF4CFC317648DD7C.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110942092, "dur":486, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110942602, "dur":724, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_2E989198C7F8F7F6.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110943327, "dur":222, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110943583, "dur":2443, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_909E97C48675646C.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110946027, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110946550, "dur":1063, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_471AEB3AC0CDB11B.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110947614, "dur":981, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110948662, "dur":1068, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110949765, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110949905, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110950054, "dur":1642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110951707, "dur":504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110952217, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352110952349, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110952515, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110953064, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110953621, "dur":655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110954307, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1754352110954554, "dur":527, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110955091, "dur":468, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110955614, "dur":507, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110956131, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110956615, "dur":458, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110957084, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110957675, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110958278, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110958961, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110959373, "dur":76, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.rsp" }}
,{ "pid":12345, "tid":8, "ts":1754352110959451, "dur":328, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110959785, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.rsp" }}
,{ "pid":12345, "tid":8, "ts":1754352110959856, "dur":521, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110960382, "dur":438, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110960850, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110961398, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110962030, "dur":1310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110963365, "dur":378, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110963768, "dur":449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110964242, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352110966738, "dur":40853, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Tool\\ForceToolSelectionDisplay.cs" }}
,{ "pid":12345, "tid":8, "ts":1754352110964738, "dur":43314, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111008053, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.BuildTestAssets.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754352111008126, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111008651, "dur":261, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.BuildTestAssets.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1754352111008913, "dur":4398, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111014617, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-processthreads-l1-1-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111015269, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111016134, "dur":632, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-memory-l1-1-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111016766, "dur":512, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-localization-l1-2-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111017282, "dur":1242, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-libraryloader-l1-1-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111019293, "dur":729, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-handle-l1-1-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111021619, "dur":1303, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-errorhandling-l1-1-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111022922, "dur":1043, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-debug-l1-1-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111023966, "dur":3851, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111027818, "dur":2150, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-2-0.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111013316, "dur":16653, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111029969, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111029969, "dur":2431, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111033913, "dur":43782, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.State\\StateGraph.cs" }}
,{ "pid":12345, "tid":8, "ts":1754352111032581, "dur":45397, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111081265, "dur":954, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Material\\LTCAreaLight\\BRDF\\BRDF_Disney.cs" }}
,{ "pid":12345, "tid":8, "ts":1754352111077978, "dur":4241, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111082545, "dur":2439, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Debug\\MonitorsDebug.cs" }}
,{ "pid":12345, "tid":8, "ts":1754352111082219, "dur":4128, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111086348, "dur":2391, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111090037, "dur":1071, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\OnMouseDown.cs" }}
,{ "pid":12345, "tid":8, "ts":1754352111088739, "dur":4093, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111092833, "dur":1806, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111095885, "dur":1038, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Serialization\\Converters\\Ray2DConverter.cs" }}
,{ "pid":12345, "tid":8, "ts":1754352111094639, "dur":3612, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111098252, "dur":533641, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111631894, "dur":1547, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.Core.Samples.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1754352111633442, "dur":2800, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111636283, "dur":2468, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/BakeryRuntimeAssembly.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1754352111638752, "dur":2768, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111641528, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111641733, "dur":10108, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111651846, "dur":1466, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111653325, "dur":11722, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":8, "ts":1754352111665052, "dur":770, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111665838, "dur":6572, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb" }}
,{ "pid":12345, "tid":8, "ts":1754352111672414, "dur":801, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111673226, "dur":8314, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.pdb" }}
,{ "pid":12345, "tid":8, "ts":1754352111681546, "dur":799, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352111682355, "dur":5917, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.pdb" }}
,{ "pid":12345, "tid":8, "ts":1754352111688274, "dur":1495865, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754352113184395, "dur":404, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aP.dag\\post-processed\\Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":8, "ts":1754352113184141, "dur":659, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":8, "ts":1754352113184820, "dur":1868, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":8, "ts":1754352113186692, "dur":3444940, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110903793, "dur":24772, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110928621, "dur":507, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0B454A1D767C04B9.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110929129, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110929497, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_DA7A54E91392079A.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110929707, "dur":764, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110930476, "dur":567, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8E5E463B70257DC0.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110931044, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110931783, "dur":434, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_302DFA822F830A98.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110932217, "dur":1139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110933361, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_B63BB0111C3635B6.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110933502, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110934112, "dur":500, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_031D764D4729D95C.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110934612, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110935134, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_A4031007901801ED.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110935373, "dur":658, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110936037, "dur":496, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_53BC6F0FFA9A0EF0.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110936534, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110937059, "dur":260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_0DE48E2CA1AF88C5.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110937319, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110937912, "dur":1129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_995BE6F3A980D53A.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110939042, "dur":1240, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110940302, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110940452, "dur":206, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110940665, "dur":51717, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754352110992383, "dur":442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110992839, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110992982, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352110993137, "dur":297, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352110993439, "dur":116967, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754352111110408, "dur":355, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111110790, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111111407, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352111111571, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111111728, "dur":605, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754352111112334, "dur":5144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111117508, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111117705, "dur":383, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352111118088, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111118247, "dur":701, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754352111118949, "dur":405, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111119364, "dur":256, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111119630, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352111119735, "dur":233, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111119998, "dur":598, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754352111120597, "dur":915, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111121522, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111121657, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754352111121747, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111121871, "dur":514, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754352111122386, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111123661, "dur":185, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\rsp\\4747638433968585886.rsp" }}
,{ "pid":12345, "tid":9, "ts":1754352111122977, "dur":869, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":9, "ts":1754352111124408, "dur":76, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111124494, "dur":458788, "ph":"X", "name": "ILPP-Configuration",  "args": { "detail":"Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":9, "ts":1754352111630195, "dur":1652, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Tayx.Graphy.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1754352111631848, "dur":1790, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111633648, "dur":1789, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.Csg.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1754352111635437, "dur":3879, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111639322, "dur":1297, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1754352111640621, "dur":1478, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111642112, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111642746, "dur":9121, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352111651876, "dur":1437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111653328, "dur":11635, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1754352111664969, "dur":1160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111666142, "dur":5905, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1754352111672053, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111672595, "dur":7561, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb" }}
,{ "pid":12345, "tid":9, "ts":1754352111680162, "dur":1623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111681799, "dur":5477, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1754352111687277, "dur":202909, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111899957, "dur":479, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":9, "ts":1754352111900436, "dur":1902, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner" }}
,{ "pid":12345, "tid":9, "ts":1754352111902338, "dur":317, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger" }}
,{ "pid":12345, "tid":9, "ts":1754352111890187, "dur":12473, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754352111902660, "dur":4728960, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110902247, "dur":26222, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110928476, "dur":606, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_74D4F77C3F3C7818.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110929083, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110929353, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_22628B5AA315C0B8.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110929489, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110930069, "dur":612, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_CA842EC65277D34E.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110930682, "dur":348, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110931035, "dur":226, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_53B1DF875DB67766.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110931262, "dur":608, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110931875, "dur":412, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_79D54DDBA2EBCB93.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110932288, "dur":937, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110933232, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_9C6431ED8A97359C.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110933357, "dur":720, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110934082, "dur":411, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_AA014B64F9FCE0C5.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110934494, "dur":391, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110934896, "dur":216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_BCDDAF0FEC79AF80.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110935112, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110935679, "dur":342, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_1C952E523A290766.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110936022, "dur":425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110936452, "dur":436, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_56B47A88FAE66068.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110936889, "dur":411, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110937304, "dur":184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_B1E2BD29E9EE7E93.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110937489, "dur":994, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110938487, "dur":470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_CE1E2BED2B6DDA54.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110938958, "dur":783, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110939745, "dur":369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_68516730823C27CA.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110940115, "dur":685, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110940808, "dur":796, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_BD747F10561B5216.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110941605, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110942035, "dur":218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8B2E3CB36710982E.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110942254, "dur":375, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110942635, "dur":529, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_5C33549B1593622B.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110943164, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110943301, "dur":1815, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D61BA8F706F39BED.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110945117, "dur":345, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110945468, "dur":375, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8715379F8421C7EF.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110945844, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110946405, "dur":621, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_EC8F7648F5B71D7C.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110947027, "dur":315, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110947348, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_08A6627B8E3EDC10.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110947492, "dur":818, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110948316, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110948439, "dur":471, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110948927, "dur":1027, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110949977, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110950574, "dur":901, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110951481, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110952116, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110952245, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110952396, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110952663, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110953107, "dur":542, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110953664, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110954329, "dur":358, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110954720, "dur":436, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110955166, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110955590, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110956132, "dur":547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110956689, "dur":347, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110957041, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110957545, "dur":560, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110958110, "dur":540, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110958688, "dur":462, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110959166, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110959593, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110960252, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110960718, "dur":865, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110961607, "dur":1015, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110962646, "dur":834, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110963515, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110964113, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/16535624540582444286.rsp" }}
,{ "pid":12345, "tid":10, "ts":1754352110964193, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110964706, "dur":1511, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110966452, "dur":521, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\External\\StlExporter\\Code\\pb_Stl.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352110970125, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"Assets\\Graphy - Ultimate Stats Monitor\\Runtime\\UI\\IModifiableState.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352110970882, "dur":3998, "ph":"X", "name": "File",  "args": { "detail":"Assets\\Graphy - Ultimate Stats Monitor\\Runtime\\UI\\G_SafeArea.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352110974881, "dur":2949, "ph":"X", "name": "File",  "args": { "detail":"Assets\\Graphy - Ultimate Stats Monitor\\Runtime\\Shader\\G_GraphShader.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352110966221, "dur":11609, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110977831, "dur":4550, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxBlendShape.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352110977831, "dur":7869, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110985701, "dur":807, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110986508, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110987197, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110987771, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110988342, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110990220, "dur":544, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Globalization.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110990764, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110991560, "dur":1344, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110992904, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110993486, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Drawing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110995683, "dur":1528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352110985701, "dur":11920, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110997623, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352110997706, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352110997888, "dur":4684, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754352111002573, "dur":1250, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111003835, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111004002, "dur":612, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProGrids.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754352111004615, "dur":1614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111006235, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Domain_Reload.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352111006308, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111006492, "dur":1001, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111007505, "dur":347, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754352111007852, "dur":948, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111008805, "dur":3866, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111014599, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpOverrides.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352111016167, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Features.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352111017202, "dur":1210, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352111019453, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Html.Abstractions.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352111012672, "dur":8898, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111022386, "dur":812, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\TestJobRunner.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352111023754, "dur":1090, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\Tasks\\SetInteractionModeTask.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352111025909, "dur":867, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\Tasks\\SaveUndoIndexTask.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352111021570, "dur":5206, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111028290, "dur":4877, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\AssemblyInfo.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352111033297, "dur":37451, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\Api\\TestRunProgress.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352111026777, "dur":45138, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111071917, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Samples.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352111071990, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111072141, "dur":297, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Samples.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754352111072439, "dur":910, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111073359, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111073476, "dur":2223, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\RenderPass\\CustomPass\\CustomPass.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352111076716, "dur":584, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\Raytracing\\Shaders\\ShaderVariablesRaytracing.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352111073476, "dur":5438, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111078914, "dur":3634, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111082767, "dur":1000, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111084150, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Ports\\ValueInputDefinition.cs" }}
,{ "pid":12345, "tid":10, "ts":1754352111083767, "dur":3070, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111086838, "dur":2862, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111089701, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352111089787, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111089916, "dur":372, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754352111090289, "dur":383, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111090683, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111090831, "dur":2787, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111093618, "dur":2638, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111096673, "dur":21035, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111117710, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754352111117861, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111118034, "dur":567, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754352111118601, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111119107, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111119260, "dur":512399, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111631661, "dur":1344, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1754352111633006, "dur":2443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111635460, "dur":1473, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.KdTree.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1754352111636933, "dur":3094, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111640035, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111640251, "dur":6783, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352111647052, "dur":323, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111647388, "dur":6856, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352111654251, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111654956, "dur":4834, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352111659797, "dur":1572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111661385, "dur":5429, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":10, "ts":1754352111666819, "dur":1221, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111668147, "dur":6570, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":10, "ts":1754352111674722, "dur":988, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111675724, "dur":6662, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.pdb" }}
,{ "pid":12345, "tid":10, "ts":1754352111682392, "dur":718, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754352111683124, "dur":5686, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.pdb" }}
,{ "pid":12345, "tid":10, "ts":1754352111688811, "dur":4942800, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110902296, "dur":26180, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110928484, "dur":977, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8328242E0F3AB252.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110929461, "dur":794, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110930260, "dur":578, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_190932711BA1EEA3.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110930838, "dur":695, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110931541, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_0A18D4A0F938414F.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110931685, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110932150, "dur":904, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_5BAC93D723A4DBDA.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110933054, "dur":956, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110934014, "dur":395, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C08780252B6B34A5.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110934410, "dur":336, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110934753, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C7BB873E67842AB9.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110934875, "dur":502, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110935385, "dur":365, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_1DC0593D81045DA0.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110935750, "dur":547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110936304, "dur":430, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C61866FD0C20A7C8.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110936735, "dur":379, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110937120, "dur":257, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_5B446FED2D793332.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110937377, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110937988, "dur":791, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_60C3EE2219920002.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110938780, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110939395, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110939938, "dur":355, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AEDEA22D251EC901.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110940294, "dur":772, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110941074, "dur":519, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_D4A801B76B5F0B73.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110941594, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110942174, "dur":489, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_53C9694894F1A206.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110942664, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110942944, "dur":617, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_30C5485055BCA2CD.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110943562, "dur":1604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110945174, "dur":584, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_C8D64AF0A731E324.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110945758, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110946475, "dur":598, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_9DC8B6831B0AD81F.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110947074, "dur":409, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110947488, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_809D789873926588.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110947657, "dur":832, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110948504, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110948692, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110948913, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110948991, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110949193, "dur":40001, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754352110989196, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110989617, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110989791, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352110989877, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352110990042, "dur":15203, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754352111005246, "dur":1125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111006384, "dur":944, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111007335, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352111007412, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111007580, "dur":9829, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754352111017410, "dur":366, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111017790, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111017931, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352111018026, "dur":181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111018212, "dur":50791, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754352111069005, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111069381, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111069530, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352111069616, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111069746, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352111069827, "dur":793, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111070626, "dur":6248, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754352111076875, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111077551, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111077726, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352111077839, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111078006, "dur":10450, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754352111088457, "dur":406, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111088884, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111089024, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754352111089136, "dur":1952, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111091097, "dur":499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754352111091597, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111092097, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111092727, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\CountItems.cs" }}
,{ "pid":12345, "tid":11, "ts":1754352111094784, "dur":1696, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\EditorBinding\\UnitSubtitleAttribute.cs" }}
,{ "pid":12345, "tid":11, "ts":1754352111092233, "dur":4621, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111096854, "dur":533341, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111630197, "dur":1650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1754352111631848, "dur":2685, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111634538, "dur":1648, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProGrids.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1754352111636187, "dur":977, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111637170, "dur":1420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1754352111638591, "dur":2467, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111641066, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111641217, "dur":6966, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352111648188, "dur":1904, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111650126, "dur":5709, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352111655840, "dur":498, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111656351, "dur":5866, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352111662222, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111662841, "dur":6283, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll" }}
,{ "pid":12345, "tid":11, "ts":1754352111669129, "dur":955, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111670120, "dur":6894, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.pdb" }}
,{ "pid":12345, "tid":11, "ts":1754352111677018, "dur":875, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111677906, "dur":6491, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb" }}
,{ "pid":12345, "tid":11, "ts":1754352111684402, "dur":951, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754352111685367, "dur":5165, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":11, "ts":1754352111690535, "dur":4941075, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110915193, "dur":13396, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110928591, "dur":605, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BBFB1E5287B23A51.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110929197, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110929754, "dur":292, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D2F857E46A033B77.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110930047, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110930598, "dur":523, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BDDBD0878436EE42.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110931121, "dur":789, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110931916, "dur":911, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_CF0C33ECD91FF12D.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110932827, "dur":1018, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110933850, "dur":401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_226FB7C693CAE0B9.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110934251, "dur":299, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110934555, "dur":249, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_38C1894821A7DE0D.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110934805, "dur":571, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110935381, "dur":407, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_360B15923733083E.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110935789, "dur":536, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110936344, "dur":484, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7332DA21F4CBB185.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110936828, "dur":425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110937260, "dur":716, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_775BDFCDF986B928.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110937977, "dur":1316, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110939343, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110939491, "dur":1011, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110940508, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BB7B92E0066DB9B4.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110940664, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110941386, "dur":409, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_84E7F529BF4B04CF.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110941796, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110942414, "dur":528, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_C9ECC274773C8C0A.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110942943, "dur":236, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110943185, "dur":507, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A0AD5069C0050C15.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110943693, "dur":1868, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110945568, "dur":446, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5BB7F80CB58C5D42.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110946014, "dur":536, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110946556, "dur":662, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_63635886138A0030.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110947219, "dur":851, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110948076, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110948223, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110948653, "dur":1110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110949785, "dur":749, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110950548, "dur":1081, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110951645, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110952207, "dur":298, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110952505, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110952685, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110953236, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110953827, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110954517, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110955081, "dur":498, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110955585, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110956304, "dur":811, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110957120, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110957715, "dur":661, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110958406, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110958994, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110959530, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110960232, "dur":424, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110960688, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110961394, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110962054, "dur":246, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/12777052554256862767.rsp" }}
,{ "pid":12345, "tid":12, "ts":1754352110962319, "dur":1172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110963516, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110964126, "dur":77, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/6102224061000461789.rsp" }}
,{ "pid":12345, "tid":12, "ts":1754352110964222, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110964837, "dur":3627, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110968465, "dur":3474, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110974387, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxPropertyEInheritType.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352110974979, "dur":751, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxPropertyEGateFit.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352110976951, "dur":632, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxPropertyDouble3.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352110971940, "dur":7327, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110979825, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110981066, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Threading.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110982234, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110982863, "dur":1402, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110985318, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110985851, "dur":833, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110986685, "dur":1224, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110987910, "dur":737, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110988647, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110989332, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110990123, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352110979267, "dur":11473, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110990741, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352110990823, "dur":264, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352110991125, "dur":16076, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754352111007202, "dur":2479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111009692, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111009876, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754352111009952, "dur":1046, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111011003, "dur":16704, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754352111027708, "dur":1915, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111029641, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111030324, "dur":2773, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352111034393, "dur":787, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352111035180, "dur":1580, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352111029781, "dur":7466, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111037248, "dur":20272, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Core\\UvUnwrapping.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111057521, "dur":963, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Core\\UvAutoManualConversion.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111058485, "dur":854, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Core\\UnwrapParameters.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111060228, "dur":806, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2\\Runtime\\Core\\Spline.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111037248, "dur":26067, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111064687, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Water\\WaterSurface\\WaterSurface.Simulation.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111066348, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Water\\WaterDecal\\WaterDecal.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111063315, "dur":3718, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111069173, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Utilities\\HDRenderUtilities.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111070769, "dur":1353, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Utilities\\CameraPositionSettings.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111067039, "dur":5083, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111072123, "dur":2958, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111075081, "dur":635, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\LineRendering\\Core\\LineRendering.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111075716, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\LineRendering\\Core\\LineRendering.Budgets.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111076637, "dur":959, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\HDRenderPipelineRuntimeResources.Migration.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111081188, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\RenderPipeline\\HDRenderPipeline.Prepass.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111075081, "dur":6776, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111081857, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Lighting\\Light\\HDLightRenderDatabase.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111083244, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@78ce765e749f\\Runtime\\Lighting\\Light\\HDAdditionalLightData.Migration.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111081857, "dur":4616, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111088979, "dur":570, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\DeprecatedVector3Add.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111086474, "dur":3614, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111090254, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnScrollRectValueChanged.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111090088, "dur":3586, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111094800, "dur":2150, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Unity\\ISingleton.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111097104, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\StickyNote\\StickyNote.cs" }}
,{ "pid":12345, "tid":12, "ts":1754352111093679, "dur":4981, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111098660, "dur":531537, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111630199, "dur":1648, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.HighDefinition.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754352111631847, "dur":1983, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111633837, "dur":1378, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Recorder.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754352111635216, "dur":1352, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111636574, "dur":1406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754352111637981, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111638206, "dur":1338, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Collections.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754352111639545, "dur":2020, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111641573, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111641721, "dur":5939, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352111647665, "dur":847, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111648524, "dur":6931, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352111655460, "dur":938, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111656410, "dur":6191, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352111662605, "dur":694, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111663311, "dur":5636, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":12, "ts":1754352111668954, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111669698, "dur":6692, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":12, "ts":1754352111676394, "dur":687, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111677093, "dur":5749, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.pdb" }}
,{ "pid":12345, "tid":12, "ts":1754352111682846, "dur":798, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754352111683711, "dur":7020, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":12, "ts":1754352111690732, "dur":4940890, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754352116642269, "dur":4143, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 54485999, "ts": 1754352116649070, "dur": 4005, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 54485999, "ts": 1754352116653190, "dur": 3452, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 54485999, "ts": 1754352116647255, "dur": 9425, "ph": "X", "name": "Write chrome-trace events", "args": {} },
