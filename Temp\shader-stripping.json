{"totalVariantsIn": 370643, "totalVariantsOut": 4144, "shaders": [{"inputVariants": 6, "outputVariants": 6, "name": "Shader Graphs/PhysicallyBasedSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 15.2032}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0621}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06760000000000001}]}]}, {"inputVariants": 434, "outputVariants": 122, "name": "Shader Graphs/Water", "pipelines": [{"inputVariants": 434, "outputVariants": 122, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.4124}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5424}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2896}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6587000000000001}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Hull)", "stripTimeMs": 0.40040000000000003}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Domain)", "stripTimeMs": 0.2988}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.04}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0322}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6931}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.446}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34750000000000003}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3289}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5184}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.053500000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0529}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.053000000000000005}]}]}, {"inputVariants": 37, "outputVariants": 20, "name": "Shader Graphs/SolidColor", "pipelines": [{"inputVariants": 37, "outputVariants": 20, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5841000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0868}, {"inputVariants": 2, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0644}, {"inputVariants": 4, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1232}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053500000000000006}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1683}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10110000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0646}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0125}, {"inputVariants": 2, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.09330000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1258}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0733}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0726}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1057}, {"inputVariants": 2, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0805}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "HDRP/DefaultFogVolume", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6326}, {"inputVariants": 2, "outputVariants": 2, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0644}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.034}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054700000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0391}, {"inputVariants": 2, "outputVariants": 2, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0685}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Sample Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.1812}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0702}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0526}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0522}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Skybox/Cubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0269}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0281}]}]}, {"inputVariants": 144, "outputVariants": 144, "name": "Hidden/HDRP/Sky/CloudLayer", "pipelines": [{"inputVariants": 144, "outputVariants": 144, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.794}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2333}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5358}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.8170000000000001}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Waveform", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.015700000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/DLSSBiasColorMask", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.46780000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027200000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/WaterCaustics", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0195}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ApplyDistortion", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.318}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020200000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CameraMotionVectors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3246}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0196}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CompositeUI", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3322}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1569}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/VoxelizeShader", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34190000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0193}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026500000000000003}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "Hidden/HDRP/DebugExposure", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0078000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/GGXConvolve", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4388}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0376}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_GGXDisneyDiffuse", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.32370000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022600000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/CoreSRP/CoreCopy", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.36100000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0458}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0218}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/UpsampleTransparent", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0313}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0194}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029300000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/Sky/GradientSky", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.40390000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0206}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0212}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CombineLighting", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3583}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0275}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0223}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ColorPyramidPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.44520000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0274}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0256}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0199}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_<PERSON>ner", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3871}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0199}]}]}, {"inputVariants": 44, "outputVariants": 24, "name": "Hidden/HDRP/OpaqueAtmosphericScattering", "pipelines": [{"inputVariants": 44, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3407}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.052500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0286}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024800000000000003}, {"inputVariants": 16, "outputVariants": 8, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1135}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0267}, {"inputVariants": 16, "outputVariants": 8, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.15230000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearBlack", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4147}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0213}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VFX/Empty", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43970000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0246}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3417}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0244}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialLoading", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.437}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.059800000000000006}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/ProbeVolumeFragmentationDebug", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.49810000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024300000000000002}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/Core/ProbeVolumeDebug", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.00030000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_CharlieFabricLambert", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3806}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0245}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CopyStencilBuffer", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.35250000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0258}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0262}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0296}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0251}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0193}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0182}]}]}, {"inputVariants": 1284, "outputVariants": 1284, "name": "Hidden/HDRP/TemporalAA", "pipelines": [{"inputVariants": 1284, "outputVariants": 1284, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41390000000000005}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.0767}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044500000000000005}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.5740000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0442}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.0442}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.043000000000000003}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.1588000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CopyDepthBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5125000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewMaterialGBuffer", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0086}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/XROcclusionMesh", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5225000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/IntegrateHDRI", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.383}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0221}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/ChromaKeying", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5081}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0381}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Renderers/Thickness", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5547000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0557}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0478}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030600000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/AlphaInjection", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3713}, {"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023700000000000002}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/DebugVTBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0106}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.007200000000000001}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/CharlieConvolve", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0082}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeToPano", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5166000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0303}]}]}, {"inputVariants": 769, "outputVariants": 257, "name": "Hidden/HDRP/FinalPass", "pipelines": [{"inputVariants": 769, "outputVariants": 257, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4591}, {"inputVariants": 768, "outputVariants": 256, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 7.988}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/VrsVisualization", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.42050000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0468}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/DownsampleDepth", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5221}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057}]}]}, {"inputVariants": 288790, "outputVariants": 810, "name": "HDRP/Lit", "pipelines": [{"inputVariants": 288790, "outputVariants": 810, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 120, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.6602000000000001}, {"inputVariants": 5184, "outputVariants": 240, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 40.0467}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2748}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.21280000000000002}, {"inputVariants": 144, "outputVariants": 48, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.4388}, {"inputVariants": 360, "outputVariants": 90, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.4133}, {"inputVariants": 72, "outputVariants": 24, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7098}, {"inputVariants": 144, "outputVariants": 36, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.6388}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2258}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2999}, {"inputVariants": 144, "outputVariants": 12, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7337}, {"inputVariants": 93312, "outputVariants": 36, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 393.4802}, {"inputVariants": 144, "outputVariants": 72, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.9706000000000001}, {"inputVariants": 186624, "outputVariants": 156, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 899.6181}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.26030000000000003}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1826}, {"inputVariants": 4, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027800000000000002}, {"inputVariants": 12, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08030000000000001}, {"inputVariants": 864, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 7.5277}, {"inputVariants": 432, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 4.808400000000001}, {"inputVariants": 864, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 8.727400000000001}, {"inputVariants": 12, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.6937}, {"inputVariants": 72, "outputVariants": 0, "variantName": "SubSurfaceDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.0148000000000001}, {"inputVariants": 6, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3556}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.2535000000000003}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/DepthValues", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5212}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08320000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.036500000000000005}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0448}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0257}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.062200000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0597}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.431}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06770000000000001}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Vectorscope", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0083}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CustomPassUtils", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43370000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0565}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0187}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0623}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0223}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0184}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugColorPicker", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.011300000000000001}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLocalVolumetricFogAtlas", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0068000000000000005}]}]}, {"inputVariants": 26, "outputVariants": 26, "name": "Hidden/HDRP/LensFlareDataDriven", "pipelines": [{"inputVariants": 26, "outputVariants": 26, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4486}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0674}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041600000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0711}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0645}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0601}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0302}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0765}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.02}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018600000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/FallbackError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.38970000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0356}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/CompositeLines", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3461}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027600000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0261}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0339}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLightVolumes", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0086}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0079}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearStencilBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4621}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025400000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ScriptableRenderPipeline/DebugDisplayHDShadowMap", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4692}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0246}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023700000000000002}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/PostProcessing/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5467000000000001}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.053700000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0222}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0611}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.02}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0466}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/LensFlareScreenSpace", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.35910000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0422}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0227}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0327}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0275}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018500000000000003}]}]}, {"inputVariants": 3, "outputVariants": 0, "name": "Hidden/HDRP/DebugHDR", "pipelines": [{"inputVariants": 3, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.006900000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 868, "outputVariants": 52, "name": "Hidden/HDRP/Sky/HDRISky", "pipelines": [{"inputVariants": 868, "outputVariants": 52, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3678}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.359}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0275}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.4174}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0213}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.3196}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.020200000000000003}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.2128}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CustomClear", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5421}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054700000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0222}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/Sky/PbrSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4118}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0468}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0238}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0344}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_CookTorrance", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41250000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031200000000000002}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/BlitColorAndDepth", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3976}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.081}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0473}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0936}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOccluder", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41390000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025900000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/WaterExclusion", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43370000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.066}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeSamplingDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3649}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0437}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/ScreenSpaceShadows", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0263}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugDisplayLatlong", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.008700000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/CoreResources/FilterAreaLightCookies", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.403}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0198}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0187}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0189}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018500000000000003}]}]}, {"inputVariants": 86, "outputVariants": 48, "name": "HDRP/Unlit", "pipelines": [{"inputVariants": 86, "outputVariants": 48, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3778}, {"inputVariants": 12, "outputVariants": 12, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1698}, {"inputVariants": 3, "outputVariants": 3, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0685}, {"inputVariants": 12, "outputVariants": 12, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1642}, {"inputVariants": 6, "outputVariants": 3, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07740000000000001}, {"inputVariants": 12, "outputVariants": 6, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1307}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0553}, {"inputVariants": 6, "outputVariants": 6, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08}, {"inputVariants": 3, "outputVariants": 0, "variantName": "DistortionVectors (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0167}, {"inputVariants": 6, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0937}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.08170000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.06810000000000001}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1265}, {"inputVariants": 2, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0755}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1023}]}]}, {"inputVariants": 16, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewTiles", "pipelines": [{"inputVariants": 16, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 16, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0482}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3825}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024200000000000003}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/CustomPassRenderersUtils", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5111}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0367}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0415}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0363}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0383}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0398}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0344}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugBlitQuad", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0132}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/HDRP/WaterDecal", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.44780000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0247}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0227}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0179}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0178}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0267}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0196}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0182}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0268}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0177}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4516}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0187}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_Ward", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3488}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0317}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOcclusionTest", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3909}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0274}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/SRP/BlitCubeTextureFace", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3985}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader Graph/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3709}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029900000000000003}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeOffsetDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4147}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0656}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/ColorResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34690000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0195}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0234}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0228}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0223}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0177}]}]}, {"inputVariants": 200, "outputVariants": 200, "name": "Hidden/HDRP/Blit", "pipelines": [{"inputVariants": 200, "outputVariants": 200, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4299}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08560000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0855}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08950000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0746}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0848}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062200000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.12000000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057300000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08750000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06910000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1268}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08850000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0654}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0631}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0517}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060700000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0707}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1022}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.053200000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058800000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061200000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0731}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.052700000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059300000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0847}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0988}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0809}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1509}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0465}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0844}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.062200000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0713}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0805}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07540000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0613}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0693}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0761}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061000000000000006}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1053}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0755}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0596}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1814}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.10600000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1731}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.156}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowClear", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.402}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024800000000000003}]}]}, {"inputVariants": 5, "outputVariants": 3, "name": "Hidden/HDRP/XRMirrorView", "pipelines": [{"inputVariants": 5, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3687}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0848}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugFullScreen", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0545}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/MotionVecResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3657}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0492}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0287}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0184}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0184}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0381}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026500000000000003}]}]}, {"inputVariants": 3, "outputVariants": 2, "name": "Hidden/HDRP/Material/Decal/DecalNormalBuffer", "pipelines": [{"inputVariants": 3, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4152}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0425}]}]}, {"inputVariants": 73, "outputVariants": 34, "name": "Shader Graphs/TIPS_Mesh 2", "pipelines": [{"inputVariants": 73, "outputVariants": 34, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6265000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1163}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0864}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.18380000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.154}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2997}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1549}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5181}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0309}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3598}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3509}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.167}, {"inputVariants": 4, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2767}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.22560000000000002}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.14200000000000002}]}]}, {"inputVariants": 59, "outputVariants": 24, "name": "Shader Graphs/TIPS_Mesh", "pipelines": [{"inputVariants": 59, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.444}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09860000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.17250000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.29860000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.12940000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09630000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2887}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1519}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2625}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.36970000000000003}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.17070000000000002}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.11460000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0903}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.16670000000000001}]}]}, {"inputVariants": 22211, "outputVariants": 90, "name": "Samples/SamplesLit_Inter", "pipelines": [{"inputVariants": 22211, "outputVariants": 90, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.8380000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.186}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3156}, {"inputVariants": 32, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.49460000000000004}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09620000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1075}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0162}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4213}, {"inputVariants": 48, "outputVariants": 12, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.2661}, {"inputVariants": 16, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.8341000000000001}, {"inputVariants": 576, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 10.2893}, {"inputVariants": 16, "outputVariants": 8, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0387}, {"inputVariants": 20736, "outputVariants": 20, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 340.5666}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.9394}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.33530000000000004}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.4355}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.4552}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.13190000000000002}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.9447}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0004}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.00030000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Unlit/Texture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0614}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0448}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "FullScreen/test", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.48560000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0308}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Distance Field", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.45880000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061900000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/EdgePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.40290000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026500000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "FullScreen/TIPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.45570000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0236}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/VertexPicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34400000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0239}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/FacePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34440000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0317}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Sprite", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5106}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07200000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/HideVertices", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4701}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022600000000000002}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "TextMeshPro/Mobile/Distance Field", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43160000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1698}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Legacy Shaders/Particles/Alpha Blended", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0882}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041800000000000004}]}]}, {"inputVariants": 21844, "outputVariants": 64, "name": "ProBuilder6/Standard Vertex Color", "pipelines": [{"inputVariants": 21844, "outputVariants": 64, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6566000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06570000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2162}, {"inputVariants": 16, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5143}, {"inputVariants": 2, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.013900000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.015000000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4319}, {"inputVariants": 24, "outputVariants": 6, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.323}, {"inputVariants": 8, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1179}, {"inputVariants": 288, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 2.193}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.31870000000000004}, {"inputVariants": 20736, "outputVariants": 16, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 112.5437}, {"inputVariants": 1, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0166}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.3991000000000002}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.24710000000000001}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.7355}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.6346000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.08120000000000001}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.1552}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0004}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 32887, "outputVariants": 138, "name": "Shader Graphs/BakeryVolumeGraph", "pipelines": [{"inputVariants": 32887, "outputVariants": 138, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.5366}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1063}, {"inputVariants": 6, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.25420000000000004}, {"inputVariants": 48, "outputVariants": 18, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.6607}, {"inputVariants": 3, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.043500000000000004}, {"inputVariants": 3, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0245}, {"inputVariants": 6, "outputVariants": 6, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.44060000000000005}, {"inputVariants": 72, "outputVariants": 18, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.7092}, {"inputVariants": 12, "outputVariants": 6, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.36460000000000004}, {"inputVariants": 864, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 6.7736}, {"inputVariants": 12, "outputVariants": 6, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5509000000000001}, {"inputVariants": 31104, "outputVariants": 24, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 173.7949}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0212}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.818}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2431}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.41450000000000004}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.1101}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1312}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.0812000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "HDRPSamples/LocalClouds", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.4613}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0472}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0333}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0463}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "CustomPass_SG/Outline", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.3108}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.040600000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0307}]}]}, {"inputVariants": 59, "outputVariants": 24, "name": "Shader Graphs/Glitch_SG", "pipelines": [{"inputVariants": 59, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5531}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1048}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.083}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1124}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0669}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1529}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10300000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1714}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0137}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.18680000000000002}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3346}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.25420000000000004}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0712}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0674}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1212}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/BillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0273}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0281}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Bark Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Leaves Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0207}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CameraFacingBillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Albedo Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0303}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023200000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Normal Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0286}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/Splatmap/Standard-BaseGen", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0252}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/TerrainEngine/PaintHeight", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0291}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0285}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0296}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0291}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0316}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0274}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0263}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0308}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/HeightBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0298}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023700000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/GenerateNormalmap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021500000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/TerrainLayerUtils", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0269}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.026600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0245}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CrossBlendNeighbors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0235}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Mobile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4349}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Standard", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.453}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.048}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0927}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0684}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0269}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044700000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0332}]}]}, {"inputVariants": 46, "outputVariants": 46, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 46, "outputVariants": 46, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.17250000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0876}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0847}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09480000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0848}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.124}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0286}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0262}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0223}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0201}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0296}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.029300000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0287}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.029400000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0281}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.028300000000000002}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08360000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0528}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0526}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0695}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.053700000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0728}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0751}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0692}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0273}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022500000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0229}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0212}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0325}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0291}]}]}, {"inputVariants": 54, "outputVariants": 54, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 54, "outputVariants": 54, "pipeline": "", "variants": [{"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4141}, {"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3739}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0398}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0334}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0359}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038200000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0524}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0443}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.030600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0281}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0264}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0287}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027200000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0241}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0291}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0235}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021400000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022500000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0227}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020300000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.026600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0262}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0252}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0258}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.023100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0223}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0212}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0279}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.021400000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0224}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0228}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.029300000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0228}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0279}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.027700000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0285}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0239}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0284}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0303}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0216}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0216}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050100000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0352}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0273}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025900000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0217}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.066}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0499}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0512}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.084}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052000000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0506}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08950000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0504}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0222}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0213}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0238}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.030100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.025500000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0238}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0303}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.030000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.021400000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0222}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0263}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0297}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.025400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.021500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0229}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.0229}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.024300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.0279}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.023100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.0327}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0466}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0318}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0235}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0251}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Geometry)", "stripTimeMs": 0.036500000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VideoComposite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/VideoDecode", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0334}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.048100000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0442}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.045700000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053500000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0322}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049600000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0442}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0432}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0337}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0426}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0455}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0451}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0216}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0217}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Compositing", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0309}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0292}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}]}]}]}