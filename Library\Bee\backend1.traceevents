{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754349971633566, "dur":3890, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349971637473, "dur":747, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349971638310, "dur":132, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1754349971638443, "dur":637, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349971639307, "dur":215, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":0, "ts":1754349971639564, "dur":103, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":0, "ts":1754349971639671, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":0, "ts":1754349971639823, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":0, "ts":1754349971641090, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971641272, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Xml.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971641622, "dur":113, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971644213, "dur":273, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971647632, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Timeline-FeaturesChecked.txt_ygu9.info" }}
,{ "pid":12345, "tid":0, "ts":1754349971650289, "dur":327, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.StreamingModule-FeaturesChecked.txt_pydr.info" }}
,{ "pid":12345, "tid":0, "ts":1754349971650651, "dur":122, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubstanceModule-FeaturesChecked.txt_rpif.info" }}
,{ "pid":12345, "tid":0, "ts":1754349971650915, "dur":152, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754349971651267, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754349971651369, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754349971651713, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UmbraModule-FeaturesChecked.txt_cv7q.info" }}
,{ "pid":12345, "tid":0, "ts":1754349971652308, "dur":153, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754349971652985, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":0, "ts":1754349971653099, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":0, "ts":1754349971653215, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":0, "ts":1754349971639117, "dur":14554, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349971653682, "dur":348564, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349972002253, "dur":108, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349972002578, "dur":3234, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754349971640780, "dur":13009, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971653796, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971654554, "dur":841, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971655396, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971656025, "dur":1085, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971657110, "dur":712, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971657823, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971658511, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971659293, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971660010, "dur":868, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971660879, "dur":962, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971661841, "dur":1147, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971662989, "dur":807, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971663796, "dur":738, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971664534, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971665208, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971665964, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971666651, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971667343, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971668093, "dur":1046, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971669140, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971653796, "dur":15943, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971670855, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Drawing.Design.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971673452, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Services.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971674405, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.OracleClient.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971675937, "dur":537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971669739, "dur":8422, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971678161, "dur":2839, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971681001, "dur":918, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971681001, "dur":3159, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971684160, "dur":1567, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971685743, "dur":750, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971686502, "dur":904, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971687413, "dur":465, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971687887, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971688455, "dur":735, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971689198, "dur":1019, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971690242, "dur":658, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971690907, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971691563, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971692225, "dur":624, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971692854, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971693573, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971694262, "dur":744, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971695010, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":1, "ts":1754349971695205, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971695368, "dur":200, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":1, "ts":1754349971695569, "dur":957, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971697792, "dur":72886, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets2.assets" }}
,{ "pid":12345, "tid":1, "ts":1754349971773512, "dur":429, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971774393, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971774736, "dur":1149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971776989, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754349971777040, "dur":650, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971779548, "dur":268, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971779830, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971779988, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971780210, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971780515, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971781992, "dur":249, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971782280, "dur":469, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971784092, "dur":955, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971786059, "dur":516, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971788169, "dur":1507, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971791601, "dur":430, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971792041, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971792521, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971792942, "dur":357, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971793337, "dur":917, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971795289, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971795604, "dur":227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971795838, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971796018, "dur":862, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971796932, "dur":867, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971798839, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971800568, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971800789, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971800929, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971801048, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971801172, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971801367, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971801664, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971804336, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754349971806958, "dur":40459, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":1, "ts":1754349971847571, "dur":154557, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971640512, "dur":13211, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971653731, "dur":770, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971654502, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971655193, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971655871, "dur":887, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971657233, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971657938, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971658620, "dur":754, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971659374, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971660035, "dur":964, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971660999, "dur":897, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971661896, "dur":1029, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971662925, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971663609, "dur":792, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971664401, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971665122, "dur":709, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971665831, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971666468, "dur":743, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971667211, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971667947, "dur":824, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971653731, "dur":15040, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971668771, "dur":880, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.TvOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971668771, "dur":5248, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971679432, "dur":556, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971679988, "dur":1537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971681525, "dur":789, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971674019, "dur":8295, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971682315, "dur":380, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971682695, "dur":3028, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971685738, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971686478, "dur":946, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971687459, "dur":761, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971688230, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971688952, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971689621, "dur":479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971690108, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971690817, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971691507, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971692175, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971692732, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971693431, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971694105, "dur":89, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt_p13n.info" }}
,{ "pid":12345, "tid":2, "ts":1754349971694196, "dur":685, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971694887, "dur":439, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971695330, "dur":263, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":2, "ts":1754349971695594, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971696326, "dur":777, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":2, "ts":1754349971697104, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971697845, "dur":72723, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":2, "ts":1754349971771355, "dur":1207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971773958, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971775387, "dur":291, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971776902, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971779416, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971781144, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterInputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754349971781202, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971782445, "dur":181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971782665, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971785114, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971785771, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971788908, "dur":809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971791658, "dur":489, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971792154, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971792649, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971793148, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971794991, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971795178, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971795463, "dur":754, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971796226, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971796407, "dur":799, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971798233, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971800266, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971801618, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971801842, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971802419, "dur":1329, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971805061, "dur":1055, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754349971807201, "dur":17617, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":2, "ts":1754349971826198, "dur":175988, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971640518, "dur":13213, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971653739, "dur":841, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971654580, "dur":746, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971655326, "dur":664, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971655990, "dur":897, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971656887, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971657456, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971658090, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971658779, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971659471, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971660188, "dur":1218, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971661406, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971662181, "dur":895, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971663077, "dur":726, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971663804, "dur":794, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971664598, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971665348, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971666102, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971666790, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971667459, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971668200, "dur":915, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":15377, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Routing.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Extensions.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Extensions.Design.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.DynamicData.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":8360, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":503, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971680166, "dur":2002, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":3, "ts":1754349971677476, "dur":7268, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971684745, "dur":969, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971685735, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971686410, "dur":997, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971687420, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971688052, "dur":835, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971688893, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971689540, "dur":955, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971690503, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971691095, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971691694, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971692366, "dur":756, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971693128, "dur":634, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971693772, "dur":845, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971694623, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971695275, "dur":418, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":3, "ts":1754349971695693, "dur":730, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971696427, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":3, "ts":1754349971696599, "dur":253, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971696886, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971769498, "dur":2812, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":3, "ts":1754349971772460, "dur":732, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971774236, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971774466, "dur":301, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971774776, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971774959, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971776531, "dur":427, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971778192, "dur":1320, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971779555, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971781046, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.LocalizationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754349971781097, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971782392, "dur":485, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971784168, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971784774, "dur":907, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971785727, "dur":778, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971788900, "dur":760, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971791240, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971791860, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971792469, "dur":1039, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971793528, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754349971793689, "dur":724, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971795456, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestTextureModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754349971795574, "dur":1227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971797683, "dur":196, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971797887, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971798254, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754349971798469, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971800366, "dur":390, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971801679, "dur":1522, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971804573, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971824693, "dur":57, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754349971806682, "dur":18086, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0.resS" }}
,{ "pid":12345, "tid":3, "ts":1754349971826685, "dur":175316, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971640469, "dur":13223, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971659325, "dur":491, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":4, "ts":1754349971659817, "dur":1911, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":4, "ts":1754349971661728, "dur":18237, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":4, "ts":1754349971679966, "dur":303, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":4, "ts":1754349971680269, "dur":3928, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":4, "ts":1754349971684198, "dur":324, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":4, "ts":1754349971684523, "dur":468, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1754349971684991, "dur":472, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":4, "ts":1754349971685463, "dur":135, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":4, "ts":1754349971685598, "dur":90, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":4, "ts":1754349971653701, "dur":32006, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971685718, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":4, "ts":1754349971685786, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971686212, "dur":769, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971686986, "dur":167, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687155, "dur":140, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687300, "dur":160, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687465, "dur":136, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687606, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971687787, "dur":369, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971688157, "dur":467, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971688626, "dur":142, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971688772, "dur":837, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971689610, "dur":206, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971689818, "dur":368, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971690188, "dur":543, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971690733, "dur":202, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971690936, "dur":236, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971691178, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971691399, "dur":225, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971691626, "dur":190, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971691818, "dur":196, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971692017, "dur":425, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971692444, "dur":806, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971693252, "dur":146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971693404, "dur":274, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971693685, "dur":227, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971693918, "dur":344, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971694264, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971694454, "dur":2212, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971696672, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971696933, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971697190, "dur":559, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971697751, "dur":320, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971698073, "dur":246, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971698325, "dur":1079, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971699406, "dur":497, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971699905, "dur":149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971700056, "dur":350, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971701057, "dur":149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":4, "ts":1754349971701207, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":4, "ts":1754349971701357, "dur":153, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":4, "ts":1754349971701511, "dur":248, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":4, "ts":1754349971685939, "dur":16179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":4, "ts":1754349971702119, "dur":15258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971729456, "dur":15047, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":4, "ts":1754349971744504, "dur":289, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971745834, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":4, "ts":1754349971745938, "dur":22457, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971768419, "dur":9892, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971778322, "dur":1214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971779544, "dur":206, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971779757, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971779992, "dur":337, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971781221, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971781377, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971781538, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971781713, "dur":280, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971782017, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971782232, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971782453, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971782659, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971785004, "dur":307, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971786300, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971786443, "dur":726, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971788802, "dur":911, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971791487, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971791890, "dur":443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971792339, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971792699, "dur":527, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971793267, "dur":608, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971794961, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971796059, "dur":1159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971798280, "dur":369, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971799830, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971801382, "dur":366, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971801758, "dur":343, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971802150, "dur":1521, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971804514, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971810880, "dur":86, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754349971806174, "dur":4800, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1" }}
,{ "pid":12345, "tid":4, "ts":1754349971811099, "dur":191083, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971640506, "dur":13209, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971653723, "dur":722, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971654445, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971655123, "dur":709, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971655833, "dur":949, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971657265, "dur":737, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971658002, "dur":729, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971658732, "dur":770, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971659503, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971660182, "dur":1163, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971661345, "dur":789, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971662135, "dur":1045, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":5, "ts":1754349971663180, "dur":676, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971663857, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971664636, "dur":716, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971665352, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971665994, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971666712, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971667417, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971668138, "dur":871, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971653723, "dur":15286, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971671278, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.TinyProfiler2.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971669010, "dur":3431, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971674837, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971677649, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971678165, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754349971672442, "dur":7059, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971679501, "dur":4178, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971683679, "dur":2105, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971685786, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971686526, "dur":933, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971687465, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971688102, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971688638, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971689389, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971689995, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971690646, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971691247, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971691898, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971692495, "dur":448, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971692952, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971693614, "dur":697, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971694320, "dur":738, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971695062, "dur":227, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":5, "ts":1754349971695290, "dur":177, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971695471, "dur":847, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":5, "ts":1754349971696319, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754349971697933, "dur":294077, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS" }}
,{ "pid":12345, "tid":5, "ts":1754349971992125, "dur":10009, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971640814, "dur":12983, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971653805, "dur":833, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971654639, "dur":745, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971655384, "dur":703, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971656088, "dur":1098, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971657187, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971657929, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971658585, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971659335, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971660076, "dur":1140, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971661216, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971662011, "dur":917, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971662928, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971663667, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971664497, "dur":702, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971665199, "dur":761, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971665961, "dur":733, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971666694, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971667373, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971668146, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971668927, "dur":957, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971653805, "dur":16079, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971671145, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.C5.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971673017, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971674311, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Tasks.v4.0.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971674962, "dur":565, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Framework.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971675967, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\ICSharpCode.SharpZipLib.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971669884, "dur":8338, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971680628, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.UnityWebRequestWWWModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971678222, "dur":4325, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971682547, "dur":3165, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971685735, "dur":686, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971686433, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971687296, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971687820, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971688497, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971689235, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971689923, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971690592, "dur":627, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971691236, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971691835, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971692492, "dur":595, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971693095, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971693768, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971694402, "dur":687, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971695093, "dur":271, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":6, "ts":1754349971695365, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971695497, "dur":851, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":6, "ts":1754349971696349, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971698431, "dur":76347, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets" }}
,{ "pid":12345, "tid":6, "ts":1754349971774927, "dur":1271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971777338, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971779969, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971780144, "dur":507, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971780683, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971782153, "dur":321, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971782480, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Numerics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754349971782624, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971784851, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971786588, "dur":1073, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971790692, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971792001, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971792448, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971793003, "dur":81, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.dll" }}
,{ "pid":12345, "tid":6, "ts":1754349971793117, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971794969, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971797203, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971797715, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971797973, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971798286, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971798444, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971798618, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971800564, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971800779, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971803545, "dur":736, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971805182, "dur":872, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754349971806915, "dur":21353, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":6, "ts":1754349971828391, "dur":173643, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971640842, "dur":12978, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971653826, "dur":798, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971654624, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971655380, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971656031, "dur":895, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971656926, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971657635, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971658293, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971658938, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971659565, "dur":699, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971660265, "dur":1208, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971661473, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971662151, "dur":817, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971662969, "dur":842, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971663812, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971664569, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971665220, "dur":743, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971665963, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971666650, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971667352, "dur":788, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971668140, "dur":971, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971653826, "dur":15285, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971669825, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\mscorlib.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971673062, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971674451, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xaml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971676201, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Windows.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971669112, "dur":8012, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971678068, "dur":853, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971680107, "dur":1435, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971681542, "dur":955, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":7, "ts":1754349971677125, "dur":7549, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971684675, "dur":1072, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971685752, "dur":767, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971686526, "dur":849, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971687380, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971687999, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971688583, "dur":732, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971689331, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971689999, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971690670, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971691325, "dur":637, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971691969, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971692555, "dur":675, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971693236, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971693861, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971694614, "dur":603, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971695221, "dur":337, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":7, "ts":1754349971695559, "dur":733, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971696300, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":7, "ts":1754349971696433, "dur":281, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971698884, "dur":74405, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":7, "ts":1754349971773501, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971773753, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971774988, "dur":1202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971777386, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971780070, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971780381, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971781957, "dur":301, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971782300, "dur":457, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971784155, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971784306, "dur":369, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971784682, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971785388, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971787373, "dur":817, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971790726, "dur":556, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971792350, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971792777, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971793427, "dur":971, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971795575, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971795800, "dur":1060, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971796892, "dur":1039, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971799091, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualEffectGraph.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754349971799143, "dur":877, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971801231, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971801385, "dur":377, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971801773, "dur":1104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971802907, "dur":884, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971804942, "dur":1021, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754349971807274, "dur":194780, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971640519, "dur":13221, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971653748, "dur":771, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971654519, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971655224, "dur":727, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971655951, "dur":866, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971656817, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971657429, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971658006, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971658720, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971659408, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971660055, "dur":1037, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971661092, "dur":843, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971661936, "dur":1182, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971663119, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971663829, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971664587, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971665368, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971666042, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971666736, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971667433, "dur":733, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971668166, "dur":1173, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":15593, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Routing.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.Formatters.Soap.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.DurableInstancing.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":504, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Caching.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971676205, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Windows.Threading.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971669340, "dur":8634, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971677975, "dur":918, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.AppContext.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971683127, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971677974, "dur":5911, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971683885, "dur":1864, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971685751, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971686435, "dur":746, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971687197, "dur":965, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971688187, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971688739, "dur":715, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971689462, "dur":540, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971690009, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971690677, "dur":675, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971691357, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971692014, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971692687, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971693402, "dur":54, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Timeline-FeaturesChecked.txt_ygu9.info" }}
,{ "pid":12345, "tid":8, "ts":1754349971693460, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971694056, "dur":764, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971694828, "dur":504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971695356, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":8, "ts":1754349971695523, "dur":841, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971696368, "dur":785, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":8, "ts":1754349971697154, "dur":272, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971697866, "dur":72694, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets" }}
,{ "pid":12345, "tid":8, "ts":1754349971772437, "dur":944, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971774220, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971774368, "dur":304, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971774682, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971774858, "dur":502, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971776337, "dur":471, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971777852, "dur":822, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971780496, "dur":677, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971782232, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971782536, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971784793, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971785417, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971788372, "dur":1132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971791745, "dur":485, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971792261, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971793031, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971794544, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971796006, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971796368, "dur":843, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971798240, "dur":451, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971799927, "dur":501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971801400, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971801800, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971802530, "dur":1121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971804687, "dur":1058, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754349971806875, "dur":18501, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0" }}
,{ "pid":12345, "tid":8, "ts":1754349971826250, "dur":175868, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971640530, "dur":13234, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971653772, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971654529, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971655239, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971655950, "dur":869, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971656819, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971657382, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971658009, "dur":733, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971658742, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971659437, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971660144, "dur":1151, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971661295, "dur":797, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971662092, "dur":920, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971663012, "dur":751, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971663763, "dur":828, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971664591, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971665230, "dur":737, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971665968, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971666636, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971667297, "dur":752, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971668049, "dur":1373, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971653772, "dur":15651, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971670141, "dur":646, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Interfaces.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971670787, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Experimental.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971671295, "dur":524, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Debugger.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971673864, "dur":500, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.WebRequest.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971674364, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.Formatting.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971676106, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Json.Microsoft.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971669423, "dur":8490, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971679635, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971680263, "dur":1460, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971681723, "dur":788, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Data.SqlClient.dll" }}
,{ "pid":12345, "tid":9, "ts":1754349971677913, "dur":6815, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971684728, "dur":991, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971685735, "dur":726, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971686467, "dur":882, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971687353, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971687513, "dur":654, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971688167, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_uyte.info" }}
,{ "pid":12345, "tid":9, "ts":1754349971688236, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971688832, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971689535, "dur":732, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971690281, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971690952, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971691581, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971692255, "dur":947, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971693210, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971693898, "dur":791, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971694694, "dur":483, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971695181, "dur":267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":9, "ts":1754349971695448, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971695621, "dur":336, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":9, "ts":1754349971695964, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971696404, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":9, "ts":1754349971696559, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971696989, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971698158, "dur":72323, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":9, "ts":1754349971770619, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971770808, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971770957, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971771139, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971772989, "dur":820, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971775282, "dur":279, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971776714, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971778211, "dur":1209, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971779440, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754349971779621, "dur":499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971781139, "dur":563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971782454, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971784595, "dur":971, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971785606, "dur":905, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971788764, "dur":1005, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971792099, "dur":449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971792554, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971793256, "dur":943, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971794961, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971795147, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971795684, "dur":326, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971796022, "dur":292, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971796354, "dur":935, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971798600, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971800356, "dur":506, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971801861, "dur":503, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971802403, "dur":1313, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971821154, "dur":83, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754349971805533, "dur":15755, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":9, "ts":1754349971821474, "dur":180744, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971640536, "dur":13246, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971653789, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971654523, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971655213, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971655921, "dur":971, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971656893, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971657545, "dur":683, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971658228, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971658890, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971659543, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971660238, "dur":1205, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971661443, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971662197, "dur":811, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971663008, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971663729, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971664520, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971665228, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971665949, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971666634, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971667302, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971668041, "dur":824, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971653789, "dur":15076, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971668866, "dur":745, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Analytics.deps.json" }}
,{ "pid":12345, "tid":10, "ts":1754349971668865, "dur":3776, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971672641, "dur":5970, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971678611, "dur":4327, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971682938, "dur":2771, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971685718, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":10, "ts":1754349971685775, "dur":724, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971686516, "dur":163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349971686692, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":10, "ts":1754349971686974, "dur":746, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971687727, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971687908, "dur":410, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971688319, "dur":460, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971688782, "dur":840, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971689624, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971689811, "dur":361, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971690178, "dur":538, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971690718, "dur":209, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971690929, "dur":250, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971691180, "dur":199, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971691397, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971691623, "dur":187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971691815, "dur":192, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971692013, "dur":449, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971692464, "dur":905, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971693374, "dur":279, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971693655, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971693834, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971693976, "dur":282, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971694260, "dur":230, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971694492, "dur":2302, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971696796, "dur":150, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971696948, "dur":219, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971697170, "dur":566, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971697738, "dur":323, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971698066, "dur":267, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971698334, "dur":684, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971699021, "dur":509, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971699532, "dur":474, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971700008, "dur":449, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":10, "ts":1754349971686505, "dur":14703, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349971702225, "dur":115, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754349971703322, "dur":239758, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349971999968, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349971999866, "dur":360, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":10, "ts":1754349972000251, "dur":1712, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":11, "ts":1754349971640491, "dur":13209, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971654114, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\WindowsBase.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971654772, "dur":724, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":11, "ts":1754349971655497, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":11, "ts":1754349971656114, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":11, "ts":1754349971657007, "dur":904, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971657911, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":11, "ts":1754349971658533, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971659257, "dur":714, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971659972, "dur":945, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":11, "ts":1754349971660918, "dur":948, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971661866, "dur":1013, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971662879, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971663538, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971664244, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971664893, "dur":719, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971665612, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971666167, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971653713, "dur":13116, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971666830, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":11, "ts":1754349971667487, "dur":760, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":11, "ts":1754349971668247, "dur":1424, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.exe" }}
,{ "pid":12345, "tid":11, "ts":1754349971672871, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.VisualStudio.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971666830, "dur":6897, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971674658, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Cryptography.Encryption.ECDsa.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971677967, "dur":841, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971680485, "dur":1201, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971681686, "dur":798, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":11, "ts":1754349971673728, "dur":9122, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971682850, "dur":2863, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971685735, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971686409, "dur":786, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971687205, "dur":644, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971687855, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971688362, "dur":701, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971689071, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971689699, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971690239, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971690927, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971691578, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971692279, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971692889, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971693561, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971694125, "dur":71, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Recorder.Base-FeaturesChecked.txt_23aq.info" }}
,{ "pid":12345, "tid":11, "ts":1754349971694199, "dur":752, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971694959, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971695099, "dur":468, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":11, "ts":1754349971695567, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971695710, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":11, "ts":1754349971695954, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971696129, "dur":278, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":11, "ts":1754349971696408, "dur":333, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971699475, "dur":75723, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":11, "ts":1754349971775534, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971777017, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971778867, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971779542, "dur":202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971779754, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971779933, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971780102, "dur":331, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781227, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781419, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781570, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781706, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971781942, "dur":366, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971783241, "dur":499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971785294, "dur":399, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971786648, "dur":808, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971788887, "dur":776, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971791309, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971791462, "dur":325, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971791797, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971792296, "dur":401, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971792705, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971793268, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971794972, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971797217, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971797580, "dur":251, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971797848, "dur":268, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971798125, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971798331, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971799928, "dur":503, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971801334, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971801523, "dur":305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971801838, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971802322, "dur":1403, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971804538, "dur":422, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754349971806140, "dur":4768, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1.resS" }}
,{ "pid":12345, "tid":11, "ts":1754349971811020, "dur":191193, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971640885, "dur":12949, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971653834, "dur":770, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971654604, "dur":752, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971655357, "dur":716, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971656074, "dur":1167, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971657241, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971657941, "dur":746, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_7.0.1323.51816.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971658687, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971659395, "dur":702, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971660098, "dur":1183, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971661281, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971662020, "dur":919, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971662939, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971663697, "dur":776, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971664474, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971665146, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971665797, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971666592, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971667259, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971668016, "dur":965, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971668981, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":12, "ts":1754349971653834, "dur":16041, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971671095, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Novell.Directory.Ldap.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971671610, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.XBuild.Tasks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971673115, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Simd.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971674493, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Profiler.Log.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971675899, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Options.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971669875, "dur":8399, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971681060, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971678275, "dur":4667, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971682943, "dur":2768, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971685737, "dur":680, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971686428, "dur":906, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971687351, "dur":813, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971688240, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971688836, "dur":707, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971689553, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971690149, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971690772, "dur":672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971691452, "dur":652, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971692111, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971692760, "dur":780, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971693547, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971694236, "dur":776, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971695017, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":12, "ts":1754349971695209, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971695356, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":12, "ts":1754349971695537, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971695717, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":12, "ts":1754349971695877, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971696076, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":12, "ts":1754349971696246, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971696380, "dur":221, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754349971696601, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971708517, "dur":67256, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":12, "ts":1754349971775906, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754349971775961, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971777415, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971780103, "dur":303, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971780445, "dur":525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971781932, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971782112, "dur":310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971782465, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971784487, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971784622, "dur":297, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971784957, "dur":414, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971786545, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971789626, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.StreamingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754349971789694, "dur":1312, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971792234, "dur":415, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971792661, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971793293, "dur":945, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971795242, "dur":351, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971795600, "dur":1120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971796754, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971798649, "dur":501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971800438, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971800621, "dur":523, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971803410, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971805198, "dur":814, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754349971807248, "dur":194869, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754349972012205, "dur":3269, "ph":"X", "name": "ProfilerWriteOutput" }
,