using System.Collections.Generic;
using UnityEngine;
using KinematicCharacterController;
namespace KinematicCharacterController.FPS
{
    public enum CharacterState
    {
        Default,
        Sliding,
        Surfing,
        Climbing,
        Vaulting,
        Vehicle
    }
    public enum OrientationMethod
    {
        TowardsCamera,
        TowardsMovement,
    }
    public struct PlayerCharacterInputs
    {
        public float MoveAxisForward;
        public float MoveAxisRight;
        public Quaternion CameraRotation;
        public bool JumpDown;
        public bool CrouchDown;
        public bool CrouchUp;
        public bool SprintDown;
        public bool SprintUp;
        
        // Vehicle inputs
        public bool IsInVehicle;
        public float VehicleThrottleAxis;
        public float VehicleVerticalAxis;
        public float VehicleRollAxis;
        public Vector2 VehicleLookInput;
        public bool VehicleBoostDown;
        public bool VehicleBoostUp;
        public bool VehicleExitRequested;
    }
    public struct AICharacterInputs
    {
        public Vector3 MoveVector;
        public Vector3 LookVector;
    }
    public enum BonusOrientationMethod
    {
        None,
        TowardsGravity,
        TowardsGroundSlopeAndGravity,
    }
    public class FPSCharacterController : Mono<PERSON>ehaviour, ICharacterController
    {
        public KinematicCharacterMotor Motor;
        public event System.Action OnLandedEvent;
        public event System.Action OnLeaveGroundEvent;
        [Header("Stable Movement")]
        public float MaxStableMoveSpeed = 10f;
        public float StableMovementSharpness = 15f;
        public float OrientationSharpness = 10f;
        public OrientationMethod OrientationMethod = OrientationMethod.TowardsCamera;
        [Header("Air Movement")]
        public float MaxAirMoveSpeed = 15f;
        public float AirAccelerationSpeed = 15f;
        public float Drag = 0.1f;
        [Header("Jumping")]
        public bool AllowJumpingWhenSliding = false;
        public float JumpUpSpeed = 10f;
        public float JumpScalableForwardSpeed = 10f;
        public float JumpPreGroundingGraceTime = 0f;
        public float JumpPostGroundingGraceTime = 0f;
        [Header("Misc")]
        public List<Collider> IgnoredColliders = new List<Collider>();
        public BonusOrientationMethod BonusOrientationMethod = BonusOrientationMethod.None;
        public float BonusOrientationSharpness = 10f;
        public Vector3 Gravity = new Vector3(0, -30f, 0);
        public Transform MeshRoot;
        public Transform CameraFollowPoint;
        [Header("Crouch Settings")]
        [Tooltip("Capsule height while crouched")]
        public float CrouchedCapsuleHeight = 1f;
        [Tooltip("Speed multiplier while crouched (e.g. 0.5 = 50% speed)")]
        public float CrouchSpeedMultiplier = 0.5f;
        private float _currentCrouchHeight = 2f; // Start at standing height
        private float _crouchLerpSpeed = 10f; // Adjust this value to control crouch speed
        private readonly float _standingHeight = 2f; // Cache the standing height

        [Header("Sprinting")]
        [Tooltip("Multiplier to the move speed while sprinting")]
        public float SprintSpeedMultiplier = 1.5f;
        [Tooltip("Sharpness/time to ramp up to sprint speed")]
        public float SprintAccelerationSpeed = 20f;
        [Header("Sliding")]
        [Tooltip("Capsule height while sliding (smaller than normal crouch if desired)")]
        public float SlideCapsuleHeight = 1f;
        [Tooltip("Extra downward slope acceleration while sliding on steep slopes")]
        public float SlideSlopeAcceleration = 15f;
        [Tooltip("Friction while sliding on flatter ground (0 = frictionless, 1+ = heavy friction)")]
        public float SlideFriction = 0.2f;
        [Tooltip("Minimum horizontal speed at which sliding continues (below this, revert to default)")]
        public float MinSlideSpeed = 2f;
        [Tooltip("Multiplier applied to gravity when sliding on ground (1.0 = normal gravity)")]
        public float SlideGravityMultiplier = 1.2f;
        [Tooltip("Multiplier applied to gravity when airborne during a slide (1.0 = normal gravity)")]
        public float SlideAirborneGravityMultiplier = 1.5f;
        [Tooltip("Speed threshold multiplier for vault-to-slide transitions (multiplied by MaxStableMoveSpeed)")]
        public float VaultSlideSpeedMultiplier = 1.3f;
        private bool _isSprinting = false;
        public bool IsSprinting => _isSprinting;
        private bool _isCrouching = false;
        public bool IsCrouching => _isCrouching;
        private bool _shouldBeCrouching = false;
        [SerializeField] private PlayerStatus playerStatus;
        public CharacterState CurrentCharacterState { get; private set; }
        private Collider[] _probedColliders = new Collider[8];
        private RaycastHit[] _probedHits = new RaycastHit[8];
        private SlidingSystem _slidingSystem;
        private SurfingSystem _surfingSystem;
        private Vector3 _moveInputVector;
        private Vector3 _lookInputVector;
        private bool _jumpRequested = false;
        private bool _jumpConsumed = false;
        private bool _jumpedThisFrame = false;
        public bool IsJumpedThisFrame => _jumpedThisFrame;
        private float _timeSinceJumpRequested = Mathf.Infinity;
        private float _timeSinceLastAbleToJump = 0f;
        private Vector3 _internalVelocityAdd = Vector3.zero;
        private float _postClimbJumpLockoutTimer = 0f;
        
        // Vehicle reference
        private SpaceshipVehicle _currentVehicle;

        private void Awake()
        {
            TransitionToState(CharacterState.Default);
            Motor.CharacterController = this;
            Motor.MaxStableSlopeAngle = 45f;
            _slidingSystem = new SlidingSystem(this, Motor, MeshRoot,
                SlideCapsuleHeight, SlideSlopeAcceleration, SlideFriction, MinSlideSpeed,
                JumpUpSpeed, JumpScalableForwardSpeed);
            _surfingSystem = new SurfingSystem(this, Motor);
            
            if (playerStatus == null)
            {
                playerStatus = GetComponent<PlayerStatus>();
                if (playerStatus == null)
                {
                    Debug.LogError("PlayerStatus component not found on the player!");
                }
            }
            if (playerStatus != null)
            {
                playerStatus.OnSprintingStarted += HandleSprintingStarted;
                playerStatus.OnSprintingStopped += HandleSprintingStopped;
            }
        }
        private void OnDestroy()
        {
            if (playerStatus != null)
            {
                playerStatus.OnSprintingStarted -= HandleSprintingStarted;
                playerStatus.OnSprintingStopped -= HandleSprintingStopped;
            }
        }
        #region State Machine
        public void TransitionToState(CharacterState newState)
        {
            CharacterState tmpInitialState = CurrentCharacterState;
            OnStateExit(tmpInitialState, newState);
            CurrentCharacterState = newState;
            OnStateEnter(newState, tmpInitialState);
        }
        public void OnStateEnter(CharacterState state, CharacterState fromState)
        {
            switch (state)
            {
                case CharacterState.Default:
                    if (fromState == CharacterState.Sliding)
                    {
                        _slidingSystem.StopSliding(_shouldBeCrouching);
                    }
                    break;
                case CharacterState.Sliding:
                    _slidingSystem.StartSliding();
                    break;
                case CharacterState.Surfing:
                    break;
                case CharacterState.Climbing:
                    break;
                case CharacterState.Vaulting:
                    break;
                case CharacterState.Vehicle:
                    // Disable character physics
                    Motor.SetMovementCollisionsSolvingActivation(false);
                    Motor.SetGroundSolvingActivation(false);
                    
                    // Hide character mesh if desired
                    if (MeshRoot != null)
                        MeshRoot.gameObject.SetActive(false);
                    break;
            }
        }
        public void OnStateExit(CharacterState state, CharacterState toState)
        {
            switch (state)
            {
                case CharacterState.Sliding:
                    _slidingSystem.StopSliding(_shouldBeCrouching);
                    break;
                case CharacterState.Surfing:
                    break;
                case CharacterState.Climbing:
                case CharacterState.Vaulting:
                    break;
                case CharacterState.Vehicle:
                    // Re-enable character physics
                    Motor.SetMovementCollisionsSolvingActivation(true);
                    Motor.SetGroundSolvingActivation(true);
                    
                    // Show character mesh
                    if (MeshRoot != null)
                        MeshRoot.gameObject.SetActive(true);
                    break;
            }
        }
        #endregion
        #region Input Handling
        public void SetInputs(ref PlayerCharacterInputs inputs)
        {
            // Handle vehicle state
            if (CurrentCharacterState == CharacterState.Vehicle)
            {
                // In vehicle mode, character doesn't process any movement
                _moveInputVector = Vector3.zero;
                _lookInputVector = Vector3.zero;
                
                // Vehicle now handles its own input directly
                // No need to pass inputs through
                return;
            }
            
            Vector3 moveInputVector = Vector3.ClampMagnitude(
                new Vector3(inputs.MoveAxisRight, 0f, inputs.MoveAxisForward),
                1f);
            Vector3 cameraPlanarDirection = Vector3.ProjectOnPlane(
                inputs.CameraRotation * Vector3.forward,
                Motor.CharacterUp).normalized;
                
            if (CurrentCharacterState == CharacterState.Climbing)
            {
                _moveInputVector = Vector3.zero;
                return;
            }
            
            if (cameraPlanarDirection.sqrMagnitude == 0f)
            {
                cameraPlanarDirection = Vector3.ProjectOnPlane(
                    inputs.CameraRotation * Vector3.up,
                    Motor.CharacterUp).normalized;
            }
            
            Quaternion cameraPlanarRotation = Quaternion.LookRotation(
                cameraPlanarDirection, Motor.CharacterUp);
            _moveInputVector = cameraPlanarRotation * moveInputVector;
            
            switch (OrientationMethod)
            {
                case OrientationMethod.TowardsCamera:
                    _lookInputVector = cameraPlanarDirection;
                    break;
                case OrientationMethod.TowardsMovement:
                    _lookInputVector = _moveInputVector.normalized;
                    break;
            }
            
            if (inputs.JumpDown)
            {
                _timeSinceJumpRequested = 0f;
                _jumpRequested = true;
                if (CurrentCharacterState == CharacterState.Surfing)
                {
                    Vector3 velocity = Motor.BaseVelocity;
                    if (_surfingSystem.HandleJump(ref velocity))
                    {
                        Motor.BaseVelocity = velocity;
                        TransitionToState(CharacterState.Default);
                        _jumpedThisFrame = true;
                        _jumpConsumed = true;
                        return;
                    }
                }
            }
            
            if (CurrentCharacterState == CharacterState.Default)
            {
                if (inputs.CrouchDown)
                {
                    bool forwardInput = (moveInputVector.z > 0.1f);

                    // Check if player is sprinting OR moving at sufficient speed to allow sliding
                    Vector3 horizontalVelocity = Vector3.ProjectOnPlane(Motor.Velocity, Motor.CharacterUp);
                    float currentSpeed = horizontalVelocity.magnitude;
                    float sprintSpeedThreshold = MaxStableMoveSpeed * 1.2f; // 20% above normal walk speed

                    bool hasSprintSpeed = _isSprinting || currentSpeed >= sprintSpeedThreshold;

                    if (forwardInput && hasSprintSpeed)
                    {
                        TransitionToState(CharacterState.Sliding);
                    }
                    else
                    {
                        _shouldBeCrouching = true;
                        _isCrouching = true;
                    }
                }
                else if (inputs.CrouchUp)
                {
                    _shouldBeCrouching = false;
                }
            }
            else if (CurrentCharacterState == CharacterState.Sliding)
            {
                if (inputs.CrouchDown)
                {
                    _shouldBeCrouching = true;
                }
                else if (inputs.CrouchUp)
                {
                    _shouldBeCrouching = false;
                    TransitionToState(CharacterState.Default);
                }
            }
            
            if (inputs.SprintDown)
            {
                playerStatus?.SetSprintingAttempt(true);
            }
            else if (inputs.SprintUp)
            {
                playerStatus?.SetSprintingAttempt(false);
            }
        }
        public void SetInputs(ref AICharacterInputs inputs)
        {
            _moveInputVector = inputs.MoveVector;
            _lookInputVector = inputs.LookVector;
        }
        #endregion
        #region KinematicCharacterController Implementation
        public void BeforeCharacterUpdate(float deltaTime)
        {
            _jumpedThisFrame = false;
        }
        public void UpdateRotation(ref Quaternion currentRotation, float deltaTime)
        {
            switch (CurrentCharacterState)
            {
                case CharacterState.Default:
                case CharacterState.Sliding:
                    {
                        if (_lookInputVector.sqrMagnitude > 0f && OrientationSharpness > 0f)
                        {
                            Vector3 smoothedLookInputDirection = Vector3.Slerp(
                                Motor.CharacterForward,
                                _lookInputVector,
                                1 - Mathf.Exp(-OrientationSharpness * deltaTime)).normalized;
                            currentRotation = Quaternion.LookRotation(smoothedLookInputDirection, Motor.CharacterUp);
                        }
                        Vector3 currentUp = (currentRotation * Vector3.up);
                        if (BonusOrientationMethod == BonusOrientationMethod.TowardsGravity)
                        {
                            Vector3 smoothedGravityDir = Vector3.Slerp(
                                currentUp,
                                -Gravity.normalized,
                                1 - Mathf.Exp(-BonusOrientationSharpness * deltaTime));
                            currentRotation = Quaternion.FromToRotation(currentUp, smoothedGravityDir) * currentRotation;
                        }
                        else if (BonusOrientationMethod == BonusOrientationMethod.TowardsGroundSlopeAndGravity)
                        {
                            if (Motor.GroundingStatus.IsStableOnGround)
                            {
                                Vector3 initialCharacterBottomHemiCenter = Motor.TransientPosition + (currentUp * Motor.Capsule.radius);
                                Vector3 smoothedGroundNormal = Vector3.Slerp(
                                    Motor.CharacterUp,
                                    Motor.GroundingStatus.GroundNormal,
                                    1 - Mathf.Exp(-BonusOrientationSharpness * deltaTime));
                                currentRotation = Quaternion.FromToRotation(currentUp, smoothedGroundNormal) * currentRotation;
                                Motor.SetTransientPosition(
                                    initialCharacterBottomHemiCenter + (currentRotation * Vector3.down * Motor.Capsule.radius));
                            }
                            else
                            {
                                Vector3 smoothedGravityDir = Vector3.Slerp(
                                    currentUp,
                                    -Gravity.normalized,
                                    1 - Mathf.Exp(-BonusOrientationSharpness * deltaTime));
                                currentRotation = Quaternion.FromToRotation(currentUp, smoothedGravityDir) * currentRotation;
                            }
                        }
                        else
                        {
                            Vector3 smoothedGravityDir = Vector3.Slerp(
                                currentUp,
                                Vector3.up,
                                1 - Mathf.Exp(-BonusOrientationSharpness * deltaTime));
                            currentRotation = Quaternion.FromToRotation(currentUp, smoothedGravityDir) * currentRotation;
                        }
                        break;
                    }
                case CharacterState.Vehicle:
                    {
                        // In vehicle mode, character rotation is handled by being parented to vehicle
                        // No need to update rotation here
                        break;
                    }
            }
        }
        public void UpdateVelocity(ref Vector3 currentVelocity, float deltaTime)
        {
            _jumpedThisFrame = false;
            _timeSinceJumpRequested += deltaTime;
            if (CurrentCharacterState != CharacterState.Surfing)
            {
                _surfingSystem.UpdateVelocity(ref currentVelocity, deltaTime, _moveInputVector);
                if (_surfingSystem.IsSurfing)
                {
                    TransitionToState(CharacterState.Surfing);
                    return;
                }
            }
            switch (CurrentCharacterState)
            {
                case CharacterState.Default:
                    {
                        float speedMultiplier = 1f;
                        if (_isCrouching)
                        {
                            speedMultiplier = CrouchSpeedMultiplier;
                        }
                        else if (_isSprinting)
                        {
                            speedMultiplier = Mathf.Lerp(
                                speedMultiplier,
                                SprintSpeedMultiplier,
                                1f - Mathf.Exp(-SprintAccelerationSpeed * deltaTime));
                        }
                        if (Motor.GroundingStatus.IsStableOnGround)
                        {
                            float currentVelocityMagnitude = currentVelocity.magnitude;
                            Vector3 effectiveGroundNormal = Motor.GroundingStatus.GroundNormal;
                            currentVelocity = Motor.GetDirectionTangentToSurface(
                                currentVelocity,
                                effectiveGroundNormal) * currentVelocityMagnitude;
                            Vector3 inputRight = Vector3.Cross(_moveInputVector, Motor.CharacterUp);
                            Vector3 reorientedInput = Vector3.Cross(
                                effectiveGroundNormal,
                                inputRight).normalized * _moveInputVector.magnitude;
                            float finalSpeed = MaxStableMoveSpeed * speedMultiplier;
                            Vector3 targetMovementVelocity = reorientedInput * finalSpeed;
                            currentVelocity = Vector3.Lerp(
                                currentVelocity,
                                targetMovementVelocity,
                                1f - Mathf.Exp(-StableMovementSharpness * deltaTime));
                        }
                        else
                        {
                            if (_moveInputVector.sqrMagnitude > 0f)
                            {
                                Vector3 addedVelocity = _moveInputVector * AirAccelerationSpeed * deltaTime;
                                Vector3 currentVelocityOnInputsPlane = Vector3.ProjectOnPlane(currentVelocity, Motor.CharacterUp);
                                if (currentVelocityOnInputsPlane.magnitude < MaxAirMoveSpeed)
                                {
                                    Vector3 newTotal = Vector3.ClampMagnitude(
                                        currentVelocityOnInputsPlane + addedVelocity,
                                        MaxAirMoveSpeed);
                                    addedVelocity = newTotal - currentVelocityOnInputsPlane;
                                }
                                else
                                {
                                    if (Vector3.Dot(currentVelocityOnInputsPlane, addedVelocity) > 0f)
                                    {
                                        addedVelocity = Vector3.ProjectOnPlane(
                                            addedVelocity,
                                            currentVelocityOnInputsPlane.normalized);
                                    }
                                }
                                if (Motor.GroundingStatus.FoundAnyGround)
                                {
                                    if (Vector3.Dot(currentVelocity + addedVelocity, addedVelocity) > 0f)
                                    {
                                        Vector3 perpenticularObstructionNormal = Vector3.Cross(
                                            Vector3.Cross(Motor.CharacterUp, Motor.GroundingStatus.GroundNormal),
                                            Motor.CharacterUp).normalized;
                                        addedVelocity = Vector3.ProjectOnPlane(addedVelocity, perpenticularObstructionNormal);
                                    }
                                }
                                currentVelocity += addedVelocity;
                            }
                            currentVelocity += Gravity * deltaTime;
                            currentVelocity *= (1f / (1f + (Drag * deltaTime)));
                        }

                        if (CurrentCharacterState != CharacterState.Climbing && _postClimbJumpLockoutTimer <= 0f)
                        {
                            if (_jumpRequested)
                            {
                                bool canActuallyJump = !_jumpConsumed &&
                                    ((AllowJumpingWhenSliding
                                          ? Motor.GroundingStatus.FoundAnyGround
                                          : Motor.GroundingStatus.IsStableOnGround)
                                     || _timeSinceLastAbleToJump <= JumpPostGroundingGraceTime);

                                if (canActuallyJump)
                                {
                                    Vector3 jumpDirection = Motor.CharacterUp;
                                    if (Motor.GroundingStatus.FoundAnyGround && !Motor.GroundingStatus.IsStableOnGround)
                                    {
                                        jumpDirection = Motor.GroundingStatus.GroundNormal;
                                    }
                                    Motor.ForceUnground();
                                    currentVelocity += (jumpDirection * JumpUpSpeed)
                                                       - Vector3.Project(currentVelocity, Motor.CharacterUp);
                                    currentVelocity += (_moveInputVector * JumpScalableForwardSpeed);
                                    _jumpRequested = false;
                                    _jumpConsumed = true;
                                    _jumpedThisFrame = true;
                                }
                            }
                        }
                        else if (_jumpRequested)
                        {
                            _jumpRequested = false;
                        }

                        if (_internalVelocityAdd.sqrMagnitude > 0f)
                        {
                            currentVelocity += _internalVelocityAdd;
                            _internalVelocityAdd = Vector3.zero;
                        }
                        break;
                    }
                case CharacterState.Sliding:
                    {
                        _slidingSystem.UpdateVelocity(ref currentVelocity, deltaTime, _moveInputVector, _jumpRequested, _jumpConsumed);
                        break;
                    }
                case CharacterState.Surfing:
                    {
                        _surfingSystem.UpdateVelocity(ref currentVelocity, deltaTime, _moveInputVector);
                        if (!_surfingSystem.IsSurfing)
                        {
                            TransitionToState(CharacterState.Default);
                        }
                        break;
                    }
                case CharacterState.Climbing:
                    {
                        currentVelocity = Vector3.zero;
                        break;
                    }
                case CharacterState.Vehicle:
                    {
                        // Character is attached to vehicle, no velocity needed
                        currentVelocity = Vector3.zero;
                        
                        // Disable gravity and physics while in vehicle
                        Motor.SetMovementCollisionsSolvingActivation(false);
                        Motor.SetGroundSolvingActivation(false);
                        break;
                    }
            }
        }
        public void AfterCharacterUpdate(float deltaTime)
        {
            if (_postClimbJumpLockoutTimer > 0f)
            {
                _postClimbJumpLockoutTimer -= deltaTime;
            }
            if (_jumpRequested && _timeSinceJumpRequested > JumpPreGroundingGraceTime)
            {
                _jumpRequested = false;
            }
            if ((AllowJumpingWhenSliding
                ? Motor.GroundingStatus.FoundAnyGround
                : Motor.GroundingStatus.IsStableOnGround))
            {
                if (!_jumpedThisFrame)
                {
                    _jumpConsumed = false;
                }
                _timeSinceLastAbleToJump = 0f;
            }
            else
            {
                _timeSinceLastAbleToJump += deltaTime;
            }
            
            switch (CurrentCharacterState)
            {
                case CharacterState.Default:
                    {
                        float targetHeight = _shouldBeCrouching ? CrouchedCapsuleHeight : _standingHeight;

                        _currentCrouchHeight = Mathf.Lerp(_currentCrouchHeight, targetHeight, 1f - Mathf.Exp(-_crouchLerpSpeed * deltaTime));

                        if (_isCrouching && !_shouldBeCrouching)
                        {
                            Motor.SetCapsuleDimensions(0.5f, _standingHeight, _standingHeight * 0.5f);
                            int overlapCount = Motor.CharacterOverlap(
                                Motor.TransientPosition,
                                Motor.TransientRotation,
                                _probedColliders,
                                Motor.CollidableLayers,
                                QueryTriggerInteraction.Ignore);

                            if (overlapCount > 0)
                            {
                                _shouldBeCrouching = true;
                                _currentCrouchHeight = CrouchedCapsuleHeight;
                            }
                            else
                            {
                                if (MeshRoot != null)
                                {
                                    MeshRoot.localScale = new Vector3(1f, _currentCrouchHeight / _standingHeight, 1f);
                                }

                                if (Mathf.Abs(_currentCrouchHeight - _standingHeight) < 0.01f)
                                {
                                    _isCrouching = false;
                                }
                            }
                        }
                        else
                        {
                            Motor.SetCapsuleDimensions(0.5f, _currentCrouchHeight, _currentCrouchHeight * 0.5f);
                            if (MeshRoot != null)
                            {
                                MeshRoot.localScale = new Vector3(1f, _currentCrouchHeight / _standingHeight, 1f);
                            }
                        }
                        break;
                    }
            }
        }
        public void PostGroundingUpdate(float deltaTime)
        {
            if (Motor.GroundingStatus.IsStableOnGround && !Motor.LastGroundingStatus.IsStableOnGround)
            {
                OnLanded();
            }
            else if (!Motor.GroundingStatus.IsStableOnGround && Motor.LastGroundingStatus.IsStableOnGround)
            {
                OnLeaveStableGround();
            }
        }
        public bool IsColliderValidForCollisions(Collider coll)
        {
            if (IgnoredColliders.Count == 0) { return true; }
            if (IgnoredColliders.Contains(coll)) { return false; }
            return true;
        }
        public void OnGroundHit(Collider hitCollider, Vector3 hitNormal, Vector3 hitPoint, ref HitStabilityReport hitStabilityReport)
        {
        }
        public void OnMovementHit(Collider hitCollider, Vector3 hitNormal, Vector3 hitPoint, ref HitStabilityReport hitStabilityReport)
        {
        }
        public void AddVelocity(Vector3 velocity)
        {
            _internalVelocityAdd += velocity;
        }
        public void ProcessHitStabilityReport(Collider hitCollider, Vector3 hitNormal, Vector3 hitPoint,
            Vector3 atCharacterPosition, Quaternion atCharacterRotation, ref HitStabilityReport hitStabilityReport)
        {
        }
        protected void OnLanded()
        {
            OnLandedEvent?.Invoke();
        }
        protected void OnLeaveStableGround()
        {
            OnLeaveGroundEvent?.Invoke();
        }
        public void OnDiscreteCollisionDetected(Collider hitCollider)
        {
        }
        #endregion
        #region Sprinting Handlers
        private void HandleSprintingStarted()
        {
            _isSprinting = true;
        }
        private void HandleSprintingStopped()
        {
            _isSprinting = false;
        }
        #endregion
        public void SetCrouchState(bool isCrouching, bool shouldBeCrouching)
        {
            _isCrouching = isCrouching;
            _shouldBeCrouching = shouldBeCrouching;
        }
        public void ConsumeJump()
        {
            _jumpRequested = false;
            _jumpConsumed = true;
            _jumpedThisFrame = true;
        }
        public void SetPostClimbJumpLockout(float seconds)
        {
            _postClimbJumpLockoutTimer = seconds;
        }
        
        /// <summary>
        /// Sets the current vehicle reference for input routing
        /// </summary>
        public void SetCurrentVehicle(SpaceshipVehicle vehicle)
        {
            _currentVehicle = vehicle;
        }
    }
}
