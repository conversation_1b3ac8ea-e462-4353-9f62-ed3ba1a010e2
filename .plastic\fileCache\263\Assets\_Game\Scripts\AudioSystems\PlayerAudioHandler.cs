using UnityEngine;
using KinematicCharacterController;
using KinematicCharacterController.FPS;
using System.Collections;

namespace AudioSystem
{
    /// <summary>
    /// Handles all player-related audio by monitoring the character controller
    /// and raising appropriate audio events.
    /// </summary>
    [RequireComponent(typeof(FPSCharacterController))]
    public class PlayerAudioHandler : MonoBehaviour
    {
        [Header("Event Channel")]
        [SerializeField] private AudioEventChannel playerAudioChannel;
        
        [Header("Movement Audio Events")]
        [SerializeField] private AudioEventDefinition footstepWalk;
        [SerializeField] private AudioEventDefinition footstepRun;        // Separate sound for running
        [SerializeField] private AudioEventDefinition footstepSlide;      // For actual sliding mechanic
        [SerializeField] private AudioEventDefinition footstepFootSlide;  // For small movements/wander (like Minecraft)
        [SerializeField] private AudioEventDefinition jumpEvent;
        [SerializeField] private AudioEventDefinition landingEvent;        // Normal landing (no damage)
        [SerializeField] private AudioEventDefinition damageLandingEvent; // Landing with damage (NEW)
        [SerializeField] private AudioEventDefinition hardLandingEvent;   // Now used for tumble-level impacts
        [SerializeField] private AudioEventDefinition tumbleImpactEvent;  // Keep for backward compatibility
        [SerializeField] private AudioEventDefinition deathLandingEvent;  // Fatal landing (NEW)
        [SerializeField] private AudioEventDefinition wallrunFootstep;
        
        [Header("Death Audio Events")]
        [SerializeField] private AudioEventDefinition deathEvent;          // General death sound (NEW)
        
        [Header("Grappling Audio Events")]
        [SerializeField] private AudioEventDefinition grappleFireEvent;
        [SerializeField] private AudioEventDefinition grappleAttachEvent;
        [SerializeField] private AudioEventDefinition grappleReleaseEvent;
        [SerializeField] private AudioEventDefinition grappleWindEvent; // Looping wind sound
        
        [Header("Fall Audio Events")]
        [SerializeField] private AudioEventDefinition fallingWindEvent; // Looping wind sound
        [SerializeField] private AudioEventDefinition deadlyFallWarningEvent; // Sound when deadly fall detected (NEW)
        
        [Header("Footstep Configuration")]
        [SerializeField] private float walkStepDistance = 0.95f;        // Distance in Unity units before triggering a step
        [SerializeField] private float runStepDistance = 0.85f;         // Slightly shorter for running (more frequent steps)
        [SerializeField] private float slideStepDistance = 1.2f;        // Longer for sliding
        [SerializeField] private float wallrunStepDistance = 0.7f;      // Shorter for wallrunning
        [SerializeField] private float minimumVelocityForFootsteps = 0.5f;

        [Header("Wander & Camera Configuration")]
        [SerializeField] private bool enableWanderSounds = true;        // Enable wander sounds like Minecraft
        [SerializeField] private float cameraRotationThreshold = 45f;   // Degrees of camera rotation to simulate body movement
        [SerializeField] private float wanderDirectionThreshold = 0.001f; // Dot product threshold for direction change detection
        [SerializeField] private float smallMovementThreshold = 0.3f;   // Speed threshold for small movements (use slide sound)
        [SerializeField] private float footSlideCooldown = 0.5f;        // Cooldown between foot slide sounds to prevent spam
        

        
        [Header("Falling Configuration")]
        [SerializeField] private float fallWindStartVelocity = -10f;
        [SerializeField] private float fallWindMaxVelocity = -30f;
        [SerializeField] private AnimationCurve fallWindVolumeCurve = AnimationCurve.Linear(0f, 0f, 1f, 1f);
        
        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = false;
        
        // Component references
        private FPSCharacterController characterController;
        private KinematicCharacterMotor motor;
        private GrapplingHookSystem grapplingHook;
        private KinematicWallRun wallRun;
        private RagdollTumbleSystem ragdollSystem;
        private HeadBob headBob;
        private FallDamageSystem fallDamageSystem;
        private PlayerStatus playerStatus;
        private FPSCharacterCamera fpsCamera;
        
        // State tracking
        private bool wasGrounded;
        private bool wasJumping;
        private bool wasGrappling;
        private float lastGroundedTime;
        private float airborneTime;
        private bool isPlayingFallWind;
        private bool isPlayingGrappleWind;
        private float lastFallVelocity;
        private SurfaceType currentSurface = SurfaceType.Default;

        // Distance-based footstep tracking
        private float distanceTraveled = 0f;
        private float distanceAtLastStep = 0f;
        private Vector3 lastPosition;
        private bool isRightFoot = false;

        // Movement state tracking for distance calculation
        private Vector3 lastMotionDirection;
        private bool lastDirectionSet = false;

        // Wander detection (like Minecraft mod)
        private Vector3 previousMotionVector;
        private bool wanderStatToggle = false;
        private bool previousMotionSet = false;

        // Camera rotation tracking for body movement simulation
        private float lastCameraYRotation;
        private bool cameraRotationInitialized = false;
        private float accumulatedCameraRotation = 0f;

        // Foot slide cooldown to prevent spam
        private float lastFootSlideTime = -1f;
        
        // Fall damage tracking
        private bool fallDamageAppliedThisLanding = false;
        private bool tumbleTriggeredThisLanding = false;
        private bool fatalFallThisLanding = false;
        private bool deadlyFallWarningPlayed = false;
        private bool deathSoundPlayed = false;
        
        // Initialization tracking
        private bool isFullyInitialized = false;
        private bool hasTriedInitialization = false;
        
        // Looping sound identifiers
        private const string FALL_WIND_SOUND_ID = "PlayerFallWind";
        private const string GRAPPLE_WIND_SOUND_ID = "PlayerGrappleWind";

        private void Awake()
        {
            // Get components
            characterController = GetComponent<FPSCharacterController>();
            motor = GetComponent<KinematicCharacterMotor>();
            grapplingHook = GetComponent<GrapplingHookSystem>();
            wallRun = GetComponent<KinematicWallRun>();
            ragdollSystem = GetComponent<RagdollTumbleSystem>();
            headBob = GetComponentInChildren<HeadBob>();
            fallDamageSystem = GetComponent<FallDamageSystem>();
            playerStatus = GetComponent<PlayerStatus>();
            fpsCamera = FindFirstObjectByType<FPSCharacterCamera>(); // Get FPS camera for rotation tracking
            
            if (enableDebugLogs)
            {
                Debug.Log($"[PlayerAudioHandler] Awake - Components found: Controller={characterController != null}, Motor={motor != null}");
            }
        }

        private void Start()
        {
            // Start the initialization coroutine to handle timing issues
            StartCoroutine(InitializeAudioSystem());
        }

        private IEnumerator InitializeAudioSystem()
        {
            // Wait a few frames to ensure all singletons are initialized
            yield return new WaitForEndOfFrame();
            yield return new WaitForEndOfFrame();
            
            // Validate audio channel
            if (playerAudioChannel == null)
            {
                Debug.LogError("[PlayerAudioHandler] No audio event channel assigned! Please assign one in the inspector.");
                yield break;
            }
            
            // Wait for GlobalAudioManager to be ready
            int attempts = 0;
            while (GlobalAudioManager.Instance == null && attempts < 100)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[PlayerAudioHandler] Waiting for GlobalAudioManager... Attempt {attempts + 1}");
                }
                yield return new WaitForSeconds(0.1f);
                attempts++;
            }
            
            if (GlobalAudioManager.Instance == null)
            {
                Debug.LogError("[PlayerAudioHandler] GlobalAudioManager not found after waiting! Audio will not work.");
                yield break;
            }
            
            if (enableDebugLogs)
            {
                Debug.Log($"[PlayerAudioHandler] GlobalAudioManager found after {attempts} attempts");
            }
            
            // Subscribe to character events
            if (characterController != null)
            {
                characterController.OnLandedEvent += HandleLanding;
                characterController.OnLeaveGroundEvent += HandleLeaveGround;
                
                if (enableDebugLogs)
                {
                    Debug.Log("[PlayerAudioHandler] Subscribed to character events");
                }
            }
            else
            {
                Debug.LogError("[PlayerAudioHandler] characterController is NULL!");
            }
            
            // Subscribe to fall damage system events
            if (fallDamageSystem != null)
            {
                fallDamageSystem.OnFallDamageApplied += HandleFallDamageApplied;
                fallDamageSystem.OnFatalFall += HandleFatalFall;
                fallDamageSystem.OnDeadlyFallDetected += HandleDeadlyFallDetected;
                fallDamageSystem.OnTumbleRequired += HandleTumbleRequired;
                
                if (enableDebugLogs)
                {
                    Debug.Log("[PlayerAudioHandler] Subscribed to fall damage system events");
                }
            }
            else
            {
                Debug.LogWarning("[PlayerAudioHandler] FallDamageSystem not found - fall damage sounds will not work properly");
            }
            
            // Subscribe to player status events
            if (playerStatus != null)
            {
                // Listen to hits changes to detect death
                playerStatus.OnHitsChanged += HandleHitsChanged;
                
                if (enableDebugLogs)
                {
                    Debug.Log("[PlayerAudioHandler] Subscribed to player status events");
                }
            }
            
            wasGrounded = motor.GroundingStatus.IsStableOnGround;

            // Initialize distance tracking
            lastPosition = transform.position;
            distanceTraveled = 0f;
            distanceAtLastStep = 0f;

            // Initialize camera rotation tracking
            if (fpsCamera != null)
            {
                lastCameraYRotation = fpsCamera.transform.eulerAngles.y;
                cameraRotationInitialized = true;
            }

            isFullyInitialized = true;
            
            if (enableDebugLogs)
            {
                Debug.Log("[PlayerAudioHandler] Fully initialized and ready");
                
                // Test audio after 2 seconds
                yield return new WaitForSeconds(2f);
                TestAudioSystem();
            }
        }

        private void TestAudioSystem()
        {
            if (jumpEvent != null && jumpEvent.eventData != null && playerAudioChannel != null)
            {
                Debug.Log("[PlayerAudioHandler] Testing audio system...");
                Debug.Log($"[PlayerAudioHandler] Jump event has {jumpEvent.eventData.clips?.Length ?? 0} clips");
                playerAudioChannel.RaiseEvent(jumpEvent.eventData);
                Debug.Log("[PlayerAudioHandler] Test audio event sent");
                
                // Also log info about footstep clips
                if (footstepWalk != null && footstepWalk.eventData != null)
                {
                    Debug.Log($"[PlayerAudioHandler] Walk footstep event has {footstepWalk.eventData.clips?.Length ?? 0} clips");
                }
            }
            else
            {
                Debug.LogError($"[PlayerAudioHandler] Audio test failed - jumpEvent: {jumpEvent != null}, eventData: {(jumpEvent?.eventData != null)}, channel: {playerAudioChannel != null}");
            }
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            if (characterController != null)
            {
                characterController.OnLandedEvent -= HandleLanding;
                characterController.OnLeaveGroundEvent -= HandleLeaveGround;
            }
            
            if (fallDamageSystem != null)
            {
                fallDamageSystem.OnFallDamageApplied -= HandleFallDamageApplied;
                fallDamageSystem.OnFatalFall -= HandleFatalFall;
                fallDamageSystem.OnDeadlyFallDetected -= HandleDeadlyFallDetected;
                fallDamageSystem.OnTumbleRequired -= HandleTumbleRequired;
            }
            
            if (playerStatus != null)
            {
                playerStatus.OnHitsChanged -= HandleHitsChanged;
            }
            
            // Stop any looping sounds
            StopFallWind();
            StopGrappleWind();
        }

        private void Update()
        {
            // Don't process audio until fully initialized
            if (!isFullyInitialized || motor == null || characterController == null) 
                return;
            
            UpdateCameraRotationTracking();
            UpdateFootsteps();
            UpdateFallingWind();
            UpdateGrappling();
            UpdateJumping();
            
            // Track grounded state
            wasGrounded = motor.GroundingStatus.IsStableOnGround;
        }

        private void UpdateFootsteps()
        {
            bool isGrounded = motor.GroundingStatus.IsStableOnGround;
            if (!isGrounded)
            {
                // Reset distance tracking when not grounded
                lastPosition = transform.position;
                return;
            }

            // Update distance traveled
            UpdateDistanceTraveled();

            // Get the player's velocity relative to what they're standing on
            Vector3 relativeVelocity = GetRelativePlayerVelocity();
            Vector3 horizontalVelocity = Vector3.ProjectOnPlane(relativeVelocity, motor.CharacterUp);
            float speed = horizontalVelocity.magnitude;

            if (speed < minimumVelocityForFootsteps) return;

            // Determine step distance threshold based on movement state
            float stepDistance = GetStepDistanceForCurrentState();

            // Check if we've traveled enough distance for a step
            float distanceSinceLastStep = distanceTraveled - distanceAtLastStep;

            if (distanceSinceLastStep >= stepDistance)
            {
                AudioEventDefinition footstepEvent = GetFootstepEventForCurrentState();

                if (footstepEvent != null)
                {
                    PlayFootstep(footstepEvent); // Use separate sound assets for different movement states

                    // Update cooldown if we played a foot slide sound
                    if (footstepEvent == footstepFootSlide)
                    {
                        lastFootSlideTime = Time.time;
                    }

                    distanceAtLastStep = distanceTraveled;
                    isRightFoot = !isRightFoot; // Alternate feet
                }
            }
        }

        /// <summary>
        /// Checks if a foot slide sound can be played (not in cooldown).
        /// </summary>
        private bool CanPlayFootSlide()
        {
            return Time.time >= lastFootSlideTime + footSlideCooldown;
        }

        /// <summary>
        /// Plays a foot slide sound and updates the cooldown timer.
        /// </summary>
        private void PlayFootSlideWithCooldown()
        {
            if (footstepFootSlide != null)
            {
                PlayFootstep(footstepFootSlide);
                lastFootSlideTime = Time.time; // Update cooldown timer
            }
        }

        /// <summary>
        /// Updates camera rotation tracking and plays foot slide when body adjusts from camera rotation.
        /// </summary>
        private void UpdateCameraRotationTracking()
        {
            if (!cameraRotationInitialized || fpsCamera == null) return;

            float currentCameraYRotation = fpsCamera.transform.eulerAngles.y;
            float rotationDelta = Mathf.DeltaAngle(lastCameraYRotation, currentCameraYRotation);

            // Accumulate rotation
            accumulatedCameraRotation += Mathf.Abs(rotationDelta);

            // If camera rotated significantly, play foot slide immediately (body adjustment)
            if (accumulatedCameraRotation >= cameraRotationThreshold)
            {
                // Play foot slide sound immediately for body adjustment (with cooldown check)
                if (motor.GroundingStatus.IsStableOnGround && CanPlayFootSlide())
                {
                    PlayFootSlideWithCooldown();

                    if (enableDebugLogs)
                    {
                        Debug.Log($"[PlayerAudioHandler] Played foot slide from camera rotation body adjustment: {accumulatedCameraRotation:F1}°");
                    }
                }

                accumulatedCameraRotation = 0f; // Reset accumulation
            }

            lastCameraYRotation = currentCameraYRotation;
        }

        /// <summary>
        /// Updates the distance traveled by the player, accounting for actual movement.
        /// Uses relative velocity to avoid counting platform movement.
        /// Includes Minecraft-style wander detection.
        /// </summary>
        private void UpdateDistanceTraveled()
        {
            // Use relative velocity instead of position difference to avoid platform movement issues
            Vector3 relativeVelocity = GetRelativePlayerVelocity();
            Vector3 horizontalVelocity = Vector3.ProjectOnPlane(relativeVelocity, motor.CharacterUp);
            float horizontalDistance = horizontalVelocity.magnitude * Time.deltaTime;

            // Minecraft-style wander detection using dot product of motion vectors
            if (horizontalVelocity.magnitude > 0.01f) // Only check if actually moving
            {
                Vector3 currentMotionVector = new Vector3(horizontalVelocity.x, 0f, horizontalVelocity.z);

                if (previousMotionSet)
                {
                    // Calculate dot product like Minecraft: scal = movX * xMovec + movZ * zMovec
                    float dotProduct = Vector3.Dot(currentMotionVector.normalized, previousMotionVector.normalized);

                    // Check if direction changed significantly (like Minecraft's scalStat logic)
                    bool directionChanged = dotProduct < wanderDirectionThreshold;

                    if (wanderStatToggle != directionChanged)
                    {
                        wanderStatToggle = directionChanged;

                        // Trigger wander sound when direction changes (like Minecraft)
                        if (wanderStatToggle && enableWanderSounds && motor.GroundingStatus.IsStableOnGround && CanPlayFootSlide())
                        {
                            PlayWanderSound();
                        }
                    }
                }

                previousMotionVector = currentMotionVector.normalized;
                previousMotionSet = true;
            }

            // Add the horizontal distance to our total
            distanceTraveled += horizontalDistance;

            // Update last position for reset tracking
            lastPosition = transform.position;

            if (enableDebugLogs && horizontalDistance > 0.01f)
            {
                Debug.Log($"[PlayerAudioHandler] Distance traveled: {distanceTraveled:F3}, This frame: {horizontalDistance:F3}, Velocity: {horizontalVelocity.magnitude:F2}");
            }
        }

        /// <summary>
        /// Gets the step distance threshold based on current movement state.
        /// </summary>
        private float GetStepDistanceForCurrentState()
        {
            // Check movement states in priority order
            if (characterController.CurrentCharacterState == CharacterState.Sliding)
            {
                return slideStepDistance;
            }
            else if (wallRun != null && wallRun.isWallRunning)
            {
                return wallrunStepDistance;
            }
            else if (characterController.IsSprinting)
            {
                return runStepDistance;
            }
            else
            {
                return walkStepDistance;
            }
        }

        /// <summary>
        /// Resets distance tracking. Call this when teleporting or respawning.
        /// </summary>
        public void ResetDistanceTracking()
        {
            distanceTraveled = 0f;
            distanceAtLastStep = 0f;
            lastPosition = transform.position;
            lastDirectionSet = false;

            // Reset wander detection
            previousMotionSet = false;
            wanderStatToggle = false;

            // Reset camera rotation tracking
            accumulatedCameraRotation = 0f;
            if (fpsCamera != null)
            {
                lastCameraYRotation = fpsCamera.transform.eulerAngles.y;
            }

            // Reset foot slide cooldown
            lastFootSlideTime = -1f;

            if (enableDebugLogs)
            {
                Debug.Log("[PlayerAudioHandler] Distance tracking reset");
            }
        }

        /// <summary>
        /// Gets the appropriate footstep event for the current movement state.
        /// </summary>
        private AudioEventDefinition GetFootstepEventForCurrentState()
        {
            // Check for actual sliding state first (the sliding mechanic)
            if (characterController.CurrentCharacterState == CharacterState.Sliding)
            {
                return footstepSlide; // Normal slide sound for actual sliding
            }
            else if (wallRun != null && wallRun.isWallRunning)
            {
                return wallrunFootstep;
            }
            else if (characterController.IsSprinting)
            {
                return footstepRun; // Use separate run sound
            }
            else
            {
                // Check movement speed to determine if it's a small movement
                Vector3 relativeVelocity = GetRelativePlayerVelocity();
                Vector3 horizontalVelocity = Vector3.ProjectOnPlane(relativeVelocity, motor.CharacterUp);
                float speed = horizontalVelocity.magnitude;

                // Small lateral movements should use foot slide sound (like Minecraft wander)
                // But only if not in cooldown to prevent spam
                if (speed > minimumVelocityForFootsteps && speed < smallMovementThreshold && CanPlayFootSlide())
                {
                    return footstepFootSlide; // Use foot slide sound for small movements
                }
                else
                {
                    return footstepWalk; // Use walk footstep for normal walking
                }
            }
        }

        /// <summary>
        /// Plays a wander sound when direction changes significantly (like Minecraft).
        /// </summary>
        private void PlayWanderSound()
        {
            // Use the foot slide sound for wander sounds (like Minecraft) with cooldown
            PlayFootSlideWithCooldown();

            if (enableDebugLogs)
            {
                Debug.Log("[PlayerAudioHandler] Played wander foot slide sound due to direction change");
            }
        }

        /// <summary>
        /// Gets the player's velocity relative to what they're standing on.
        /// If on a moving platform, this returns the velocity relative to the platform.
        /// </summary>
        private Vector3 GetRelativePlayerVelocity()
        {
            Vector3 playerVelocity = motor.Velocity;

            // Check if we're standing on something
            if (motor.GroundingStatus.IsStableOnGround && motor.GroundingStatus.GroundCollider != null)
            {
                // Check if it's a moving platform (has PhysicsMover component)
                PhysicsMover mover = motor.GroundingStatus.GroundCollider.GetComponent<PhysicsMover>();
                if (mover != null)
                {
                    // Subtract the platform's velocity to get relative velocity
                    playerVelocity -= mover.Velocity;

                    if (enableDebugLogs)
                    {
                        Debug.Log($"[PlayerAudioHandler] On moving platform. Platform velocity: {mover.Velocity.magnitude:F2}, Relative velocity: {playerVelocity.magnitude:F2}");
                    }
                }
            }

            return playerVelocity;
        }

        private void PlayFootstep(AudioEventDefinition footstepEvent)
        {
            // Early exit if audio system not ready
            if (!isFullyInitialized || playerAudioChannel == null) 
            {
                if (enableDebugLogs)
                {
                    Debug.LogWarning("[PlayerAudioHandler] Tried to play footstep but audio system not ready");
                }
                return;
            }
            
            DetectSurface();
            
            // Get event data
            AudioEventData eventData = footstepEvent.eventData;
            if (eventData == null)
            {
                Debug.LogError($"The AudioEventDefinition asset '{footstepEvent.name}' is missing its Event Data. Please check it in the Inspector.", footstepEvent);
                return;
            }
            
            // Check if we need to use surface-specific clips
            bool hasSurfaceVariations = footstepEvent.surfaceVariations != null && footstepEvent.surfaceVariations.Length > 0;
            if (hasSurfaceVariations && currentSurface != SurfaceType.Default)
            {
                // Get surface-specific clip
                AudioClip surfaceClip = footstepEvent.GetClip(currentSurface);
                if (surfaceClip != null)
                {
                    // Create a copy of eventData to avoid modifying the original
                    eventData = CreateEventDataCopy(footstepEvent.eventData);
                    // Use the surface-specific clip
                    eventData.clips = new AudioClip[] { surfaceClip };
                }
            }
            // Otherwise, use the original eventData with all its clips for randomization
            
            // Play at foot position
            Vector3 footPosition = transform.position - (motor.CharacterUp * motor.Capsule.height * 0.5f);
            playerAudioChannel.RaisePositionalEvent(eventData, footPosition);
            
            if (enableDebugLogs)
            {
                Debug.Log($"[PlayerAudioHandler] Played footstep: {footstepEvent.name}, Clips available: {eventData.clips?.Length ?? 0}");
            }
        }

        private void DetectSurface()
        {
            // Raycast down to detect surface type
            RaycastHit hit;
            Vector3 rayStart = transform.position;
            float rayDistance = motor.Capsule.height * 0.5f + 0.5f;
            
            if (Physics.Raycast(rayStart, -motor.CharacterUp, out hit, rayDistance, motor.CollidableLayers))
            {
                // Check for surface tag or material
                // This is a simplified version - you'd expand this based on your game's needs
                /*
                if (hit.collider.CompareTag("Wood"))
                    currentSurface = SurfaceType.Wood;
                else if (hit.collider.CompareTag("Metal"))
                    currentSurface = SurfaceType.Metal;
                else if (hit.collider.CompareTag("Grass"))
                    currentSurface = SurfaceType.Grass;
                else
                    currentSurface = SurfaceType.Concrete; // Default
                */
                currentSurface = SurfaceType.Concrete;
            }
        }

        private void UpdateFallingWind()
        {
            if (!isFullyInitialized) return;
            
            float currentFallVelocity = motor.Velocity.y;
            bool shouldPlayWind = currentFallVelocity < fallWindStartVelocity && !motor.GroundingStatus.IsStableOnGround;
            
            if (shouldPlayWind && !isPlayingFallWind)
            {
                StartFallWind();
            }
            else if (!shouldPlayWind && isPlayingFallWind)
            {
                StopFallWind();
            }
            
            lastFallVelocity = currentFallVelocity;
        }

        private void StartFallWind()
        {
            if (!isFullyInitialized || fallingWindEvent == null) return;
            
            // Set unique name for this looping sound
            var modifiedEventData = fallingWindEvent.eventData;
            modifiedEventData.eventName = FALL_WIND_SOUND_ID;
            
            var parameters = new AudioPlaybackParams { loop = true };
            playerAudioChannel.RaiseParameterizedEvent(modifiedEventData, parameters);
            isPlayingFallWind = true;
        }

        private void StopFallWind()
        {
            if (isPlayingFallWind && GlobalAudioManager.Instance != null)
            {
                GlobalAudioManager.Instance.FadeOutLoopingSound(FALL_WIND_SOUND_ID, 0.5f);
                isPlayingFallWind = false;
            }
        }

        private void UpdateGrappling()
        {
            if (!isFullyInitialized || grapplingHook == null) return;
            
            bool isGrapplingNow = grapplingHook.IsSwinging;
            
            // Grapple started
            if (isGrapplingNow && !wasGrappling)
            {
                if (grappleAttachEvent != null)
                {
                    playerAudioChannel.RaiseEvent(grappleAttachEvent.eventData);
                }
                
                StartGrappleWind();
            }
            // Grapple ended
            else if (!isGrapplingNow && wasGrappling)
            {
                if (grappleReleaseEvent != null)
                {
                    playerAudioChannel.RaiseEvent(grappleReleaseEvent.eventData);
                }
                
                StopGrappleWind();
            }
            
            wasGrappling = isGrapplingNow;
        }

        private void StartGrappleWind()
        {
            if (!isFullyInitialized || grappleWindEvent == null) return;
            
            // Set unique name for this looping sound
            var modifiedEventData = grappleWindEvent.eventData;
            modifiedEventData.eventName = GRAPPLE_WIND_SOUND_ID;
            
            var parameters = new AudioPlaybackParams { loop = true };
            playerAudioChannel.RaiseParameterizedEvent(modifiedEventData, parameters);
            isPlayingGrappleWind = true;
        }

        private void StopGrappleWind()
        {
            if (isPlayingGrappleWind && GlobalAudioManager.Instance != null)
            {
                GlobalAudioManager.Instance.FadeOutLoopingSound(GRAPPLE_WIND_SOUND_ID, 0.3f);
                isPlayingGrappleWind = false;
            }
        }

        private void UpdateJumping()
        {
            if (!isFullyInitialized) return;
            
            bool jumpedThisFrame = characterController.IsJumpedThisFrame;
            
            if (jumpedThisFrame && jumpEvent != null)
            {
                playerAudioChannel.RaiseEvent(jumpEvent.eventData);
            }
        }

        private void HandleLanding()
        {
            if (!isFullyInitialized) return;

            // Stop falling wind sound
            StopFallWind();

            // Reset deadly fall warning flag
            deadlyFallWarningPlayed = false;

            // Reset distance tracking to prevent immediate footstep after landing
            ResetDistanceTracking();

            // Let the fall damage system determine what kind of landing this was
            // We'll play the appropriate sound based on the events we receive

            // Wait a frame to see if fall damage events fire
            StartCoroutine(CheckForNormalLanding());

            // Reset air time
            airborneTime = 0f;
        }

        private IEnumerator CheckForNormalLanding()
        {
            // Wait one frame to allow fall damage events to fire
            yield return new WaitForEndOfFrame();
            
            // If no fall damage events fired, this was a normal landing
            if (!fallDamageAppliedThisLanding && !tumbleTriggeredThisLanding && !fatalFallThisLanding)
            {
                PlayNormalLanding();
            }
            
            // Reset flags for next landing
            fallDamageAppliedThisLanding = false;
            tumbleTriggeredThisLanding = false;
            fatalFallThisLanding = false;
        }

        private void PlayNormalLanding()
        {
            if (landingEvent == null) return;
            
            // Normal landing with no damage
            float landingVelocity = lastFallVelocity;
            float intensity = Mathf.InverseLerp(0f, -5f, landingVelocity);
            
            AudioEventData eventData = CreateEventDataForVariations(landingEvent, intensity);
            
            var parameters = new AudioPlaybackParams 
            { 
                volumeMultiplier = 0.3f + (intensity * 0.4f)
            };
            
            playerAudioChannel.RaiseParameterizedEvent(eventData, parameters);
            
            if (enableDebugLogs)
            {
                Debug.Log($"[PlayerAudioHandler] Played normal landing sound, velocity: {landingVelocity}");
            }
        }

        private void HandleLeaveGround()
        {
            lastGroundedTime = Time.time;
        }

        // Fall damage system event handlers
        private void HandleFallDamageApplied()
        {
            if (!isFullyInitialized || damageLandingEvent == null) return;
            
            fallDamageAppliedThisLanding = true;
            
            // Play damage landing sound
            float intensity = 0.7f; // Medium intensity for damage landing
            
            AudioEventData eventData = CreateEventDataForVariations(damageLandingEvent, intensity);
            
            var parameters = new AudioPlaybackParams 
            { 
                volumeMultiplier = 0.8f
            };
            
            playerAudioChannel.RaiseParameterizedEvent(eventData, parameters);
            
            if (enableDebugLogs)
            {
                Debug.Log("[PlayerAudioHandler] Played damage landing sound");
            }
        }

        private void HandleFatalFall()
        {
            if (!isFullyInitialized || deathLandingEvent == null) return;
            
            fatalFallThisLanding = true;
            
            // Play fatal landing sound
            AudioEventData eventData = CreateEventDataForVariations(deathLandingEvent, 1f);
            
            var parameters = new AudioPlaybackParams 
            { 
                volumeMultiplier = 1f
            };
            
            playerAudioChannel.RaiseParameterizedEvent(eventData, parameters);
            
            if (enableDebugLogs)
            {
                Debug.Log("[PlayerAudioHandler] Played fatal landing sound");
            }
        }

        private void HandleDeadlyFallDetected()
        {
            if (!isFullyInitialized || deadlyFallWarningEvent == null || deadlyFallWarningPlayed) return;
            
            deadlyFallWarningPlayed = true;
            
            // Play a warning sound when a deadly fall is detected mid-air
            playerAudioChannel.RaiseEvent(deadlyFallWarningEvent.eventData);
            
            if (enableDebugLogs)
            {
                Debug.Log("[PlayerAudioHandler] Played deadly fall warning sound");
            }
        }

        private void HandleTumbleRequired(float impactVelocity, Vector3 horizontalVelocity)
        {
            if (!isFullyInitialized) return;
            
            tumbleTriggeredThisLanding = true;
            
            // Use tumbleImpactEvent if available, otherwise fall back to hardLandingEvent
            AudioEventDefinition tumbleEvent = tumbleImpactEvent != null ? tumbleImpactEvent : hardLandingEvent;
            
            if (tumbleEvent == null) return;
            
            // Play tumble impact sound with intensity based on impact
            float intensity = Mathf.InverseLerp(-12f, -25f, impactVelocity);
            
            AudioEventData eventData = CreateEventDataForVariations(tumbleEvent, intensity);
            
            var parameters = new AudioPlaybackParams 
            { 
                volumeMultiplier = 0.7f + (intensity * 0.3f)
            };
            
            playerAudioChannel.RaiseParameterizedEvent(eventData, parameters);
            
            if (enableDebugLogs)
            {
                Debug.Log($"[PlayerAudioHandler] Played tumble landing sound, impact velocity: {impactVelocity}");
            }
        }

        private void HandleHitsChanged(int oldHits, int newHits)
        {
            // Check if player just died (hits reached max)
            if (playerStatus != null && newHits >= playerStatus.MaxHits && oldHits < playerStatus.MaxHits && !deathSoundPlayed)
            {
                deathSoundPlayed = true;
                HandlePlayerDeath();
            }
            // Reset death sound flag when player respawns (hits reset to 0)
            else if (newHits == 0 && oldHits > 0)
            {
                deathSoundPlayed = false;
            }
        }

        private void HandlePlayerDeath()
        {
            if (!isFullyInitialized || deathEvent == null) return;
            
            // Play general death sound
            playerAudioChannel.RaiseEvent(deathEvent.eventData);
            
            // Stop any looping sounds
            StopFallWind();
            StopGrappleWind();
            
            if (enableDebugLogs)
            {
                Debug.Log("[PlayerAudioHandler] Played death sound");
            }
        }

        // Helper method to create AudioEventData for variations
        private AudioEventData CreateEventDataForVariations(AudioEventDefinition eventDef, float intensity)
        {
            AudioEventData eventData = eventDef.eventData;
            
            // Check if we have intensity or surface variations
            bool hasIntensityVariations = eventDef.intensityVariations != null && eventDef.intensityVariations.Length > 0;
            bool hasSurfaceVariations = eventDef.surfaceVariations != null && eventDef.surfaceVariations.Length > 0;
            
            if (hasIntensityVariations || (hasSurfaceVariations && currentSurface != SurfaceType.Default))
            {
                // Get specific clip based on intensity/surface
                AudioClip specificClip = eventDef.GetClip(currentSurface, intensity);
                if (specificClip != null)
                {
                    // Create a copy of eventData to avoid modifying the original
                    eventData = CreateEventDataCopy(eventDef.eventData);
                    // Use the specific clip
                    eventData.clips = new AudioClip[] { specificClip };
                }
            }
            
            return eventData;
        }

        // Helper method to create a copy of AudioEventData
        private AudioEventData CreateEventDataCopy(AudioEventData original)
        {
            AudioEventData copy = new AudioEventData();
            copy.eventName = original.eventName;
            copy.baseVolume = original.baseVolume;
            copy.basePitch = original.basePitch;
            copy.volumeVariation = original.volumeVariation;
            copy.pitchVariation = original.pitchVariation;
            copy.mixerGroup = original.mixerGroup;
            copy.is3D = original.is3D;
            copy.minDistance = original.minDistance;
            copy.maxDistance = original.maxDistance;
            // Don't copy clips here - they'll be set separately
            return copy;
        }

        // Public method to trigger grapple fire sound
        public void OnGrappleFire()
        {
            if (!isFullyInitialized || grappleFireEvent == null) return;
            
            playerAudioChannel.RaiseEvent(grappleFireEvent.eventData);
        }
    }
}