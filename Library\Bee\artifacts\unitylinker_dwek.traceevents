{ "pid": 239640, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 239640, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 239640, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 239640, "tid": 1, "ts": 1754352469443175, "dur": 422552, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 239640, "tid": 1, "ts": 1754352469445489, "dur": 70867, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469455195, "dur": 59073, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469542291, "dur": 15337, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469558899, "dur": 79040, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469638001, "dur": 19559, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469657581, "dur": 198352, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469862802, "dur": 2691, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469865729, "dur": 456, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469873886, "dur": 2676, "ph": "X", "name": "", "args": {} },
{ "pid": 239640, "tid": 1, "ts": 1754352469873112, "dur": 3844, "ph": "X", "name": "Write chrome-trace events", "args": {} },
